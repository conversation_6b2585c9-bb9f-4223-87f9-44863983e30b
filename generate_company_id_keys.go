package main

import (
	"log"
)

func main() {
	log.Println("Generating RSA key pair for company ID encryption...")
	
	if err := GenerateCompanyIDKeys(); err != nil {
		log.Fatalf("Failed to generate company ID keys: %v", err)
	}
	
	log.Println("✅ Company ID encryption keys generated successfully!")
	log.Println("📁 Files created:")
	log.Println("   - company_id_encryption_private_key.pem (for encryption)")
	log.Println("   - company_id_encryption_public_key.pem (for decryption in validator)")
}
