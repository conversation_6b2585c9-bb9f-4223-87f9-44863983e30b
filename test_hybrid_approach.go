// 测试混合方案：显示一致的MachineID + 正确的签名验证
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"strings"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type MachineInfo struct {
	CompanyName string `json:"CompanyName"`
	Email       string `json:"Email"`
	Phone       string `json:"Phone"`
	GeneratedBy string `json:"GeneratedBy"`
	MachineID   string `json:"MachineID"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func extractSoftwareAndVersion(generatedBy string) (string, string) {
	parts := strings.Fields(generatedBy)
	var softwareParts []string
	var version string
	
	for _, part := range parts {
		if len(part) > 1 && (strings.ToLower(part[:1]) == "v") {
			versionPart := part[1:]
			if containsVersionPattern(versionPart) {
				version = versionPart
				break
			}
		}
		softwareParts = append(softwareParts, part)
	}
	
	softwareName := strings.Join(softwareParts, " ")
	if version == "" {
		return generatedBy, ""
	}
	return softwareName, version
}

func containsVersionPattern(s string) bool {
	hasDigit := false
	for _, char := range s {
		if char >= '0' && char <= '9' {
			hasDigit = true
		} else if char != '.' && char != '-' && char != '_' {
			return false
		}
	}
	return hasDigit
}

func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}
	
	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}
	
	return privateKey, nil
}

func getRawMachineID(machineID string, privateKey *rsa.PrivateKey) (string, error) {
	// 检查是否是Base64编码的加密数据
	if decodedData, err := base64.StdEncoding.DecodeString(machineID); err == nil && len(decodedData) > 100 {
		// 尝试解密
		decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, decodedData, nil)
		if err != nil {
			// 解密失败，当作原始ID处理
			return machineID, nil
		}
		return string(decryptedData), nil
	}
	// 不是加密数据，直接返回
	return machineID, nil
}

func createSignature(license *LicenseData, rawMachineID string, privateKey *rsa.PrivateKey) (string, error) {
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return "", fmt.Errorf("failed to parse expiration date: %v", err)
	}
	
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(rawMachineID),
	}
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal signature data: %v", err)
	}
	
	fmt.Printf("📋 Signature JSON: %s\n", string(jsonData))
	
	hash := sha256.Sum256(jsonData)
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to sign data: %v", err)
	}
	
	return base64.StdEncoding.EncodeToString(signature), nil
}

func validateSignature(license *LicenseData, privateKey *rsa.PrivateKey) error {
	// 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}
	
	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}
	
	// 重构签名数据
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}
	
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}
	
	fmt.Printf("📋 Validation JSON: %s\n", string(jsonData))
	
	// 验证签名
	hash := sha256.Sum256(jsonData)
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}
	
	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	
	return nil
}

func main() {
	fmt.Println("🚀 Testing Hybrid Approach: Consistent MachineID + Valid Signature")
	fmt.Println("===================================================================")
	
	// 1. 加载机器信息
	machineData, err := os.ReadFile("factory_machine_info.json")
	if err != nil {
		fmt.Printf("❌ Failed to read machine info: %v\n", err)
		return
	}
	
	var machineInfo MachineInfo
	err = json.Unmarshal(machineData, &machineInfo)
	if err != nil {
		fmt.Printf("❌ Failed to parse machine info: %v\n", err)
		return
	}
	
	fmt.Printf("📂 Machine Info:\n")
	fmt.Printf("  Company: %s\n", machineInfo.CompanyName)
	fmt.Printf("  Email: %s\n", machineInfo.Email)
	fmt.Printf("  MachineID (encrypted): %s...\n", machineInfo.MachineID[:50])
	
	// 2. 加载私钥
	privateKey, err := loadPrivateKey()
	if err != nil {
		fmt.Printf("❌ Failed to load private key: %v\n", err)
		return
	}
	
	// 3. 获取原始机器ID（用于签名）
	rawMachineID, err := getRawMachineID(machineInfo.MachineID, privateKey)
	if err != nil {
		fmt.Printf("❌ Failed to get raw machine ID: %v\n", err)
		return
	}
	fmt.Printf("🔓 Raw Machine ID (for signature): %s\n", rawMachineID)
	
	// 4. 提取软件名称和版本
	softwareName, version := extractSoftwareAndVersion(machineInfo.GeneratedBy)
	fmt.Printf("📦 Software: %s\n", softwareName)
	fmt.Printf("🏷️ Version: %s\n", version)
	
	// 5. 创建许可证数据（混合方案）
	expirationDate := time.Now().AddDate(0, 6, 0)
	
	license := &LicenseData{
		CompanyName:        machineInfo.CompanyName,
		Email:              machineInfo.Email,
		Phone:              machineInfo.Phone,
		AuthorizedSoftware: softwareName,
		AuthorizedVersion:  version,
		ExpirationDate:     expirationDate.Format("2006-01-02"),
		IssuedDate:         time.Now().Format("2006-01-02"),
		// 关键：使用机器信息文件中的SAME加密MachineID
		EncryptedMachineID: machineInfo.MachineID,
	}
	
	fmt.Printf("\n🔑 Key Strategy:\n")
	fmt.Printf("  - License MachineID: SAME as machine info file (for user verification)\n")
	fmt.Printf("  - Signature creation: Uses RAW machine ID (for correct validation)\n")
	
	// 6. 创建签名（使用原始机器ID）
	fmt.Println("\n🔐 Creating Signature with Raw Machine ID...")
	signature, err := createSignature(license, rawMachineID, privateKey)
	if err != nil {
		fmt.Printf("❌ Failed to create signature: %v\n", err)
		return
	}
	license.Signature = signature
	
	// 7. 保存许可证
	jsonData, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		fmt.Printf("❌ Failed to marshal license: %v\n", err)
		return
	}
	
	err = os.WriteFile("hybrid_factory_license.json", jsonData, 0644)
	if err != nil {
		fmt.Printf("❌ Failed to save license: %v\n", err)
		return
	}
	
	fmt.Println("\n✅ Hybrid License Created Successfully!")
	fmt.Printf("💾 Saved to: hybrid_factory_license.json\n")
	
	// 8. 验证MachineID一致性
	fmt.Println("\n🔍 Verifying MachineID Consistency...")
	if license.EncryptedMachineID == machineInfo.MachineID {
		fmt.Println("✅ License MachineID matches machine info file!")
		fmt.Println("   Users can verify this license is for their machine.")
	} else {
		fmt.Println("❌ License MachineID does not match machine info file!")
	}
	
	// 9. 立即验证签名
	fmt.Println("\n🧪 Testing Signature Validation...")
	err = validateSignature(license, privateKey)
	if err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
	} else {
		fmt.Println("✅ Signature validation successful!")
	}
	
	fmt.Println("\n🎉 Hybrid Approach Success!")
	fmt.Println("✅ MachineID consistency: License shows same encrypted ID as machine info")
	fmt.Println("✅ Signature validation: Uses raw machine ID for correct verification")
	fmt.Println("✅ Best of both worlds: User verification + Authorized software validation")
}
