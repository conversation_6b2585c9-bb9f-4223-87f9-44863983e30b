# Quick Update Guide - License Validator
# 快速更新指南 - 许可证验证器

## 🚨 **紧急更新通知**

**问题**: 您当前的验证器无法验证V25生成的许可证
**原因**: 密钥架构已从单一密钥升级到分离密钥
**解决**: 立即更新到V23验证器

## ⚡ **3步快速更新**

### **步骤1: 替换验证器文件**
```bash
# 备份当前验证器
cp your_validator.go your_validator_backup.go

# 使用新的V23验证器
cp V23_LICENSE_VALIDATOR.go your_validator.go
```

### **步骤2: 更新密钥常量**
在您的验证器中，确保使用以下密钥：

```go
// 机器ID解密私钥 (保持不变)
V23_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWs
BI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl
7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfC
n9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85j
j/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrk
Plt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB...
-----END RSA PRIVATE KEY-----`

// 签名验证公钥 (新的独立密钥) ⭐ 关键更新
V23_SIGNING_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAb
-----END RSA PUBLIC KEY-----`
```

### **步骤3: 更新验证调用**
```go
// 旧调用 (需要替换)
err := ValidateLicenseFile("license.json")

// 新调用 (使用这个)
err := ValidateV23LicenseFile("license.json")
```

## 🔑 **关键变更说明**

### **密钥分离架构**
```
之前 (单一密钥):
签名验证公钥 = 机器ID解密私钥 (同一密钥对) ❌

现在 (分离密钥):
签名验证公钥 ≠ 机器ID解密私钥 (不同密钥对) ✅
```

### **新支持的许可证格式**
```json
{
  "company_name": "...",
  "email": "...",
  "license_type": "lease",      // 新字段
  "start_date": "2025-07-12",   // 新字段
  "expiration_date": "2026-07-12",
  "encrypted_machine_id": "...",
  "signature": "..."            // 使用新密钥签名
}
```

## ✅ **验证更新成功**

### **测试命令**
```bash
# 编译更新后的软件
go build -o your_software_v23 main.go your_validator.go

# 测试V25许可证验证
./your_software_v23
```

### **预期结果**
```
✅ License validation successful
✅ Machine binding validation successful  
✅ Digital signature validation successful
🎉 Software is authorized, starting...
```

## 🚨 **如果仍然失败**

### **检查清单**
- [ ] 确认使用了新的`V23_SIGNING_PUBLIC_KEY`
- [ ] 确认许可证包含`license_type`和`start_date`字段
- [ ] 确认许可证是由V25生成器创建的
- [ ] 确认在正确的机器上运行

### **常见错误及解决**
```
错误: "signature verification failed"
解决: 检查是否使用了新的签名公钥

错误: "required field missing"  
解决: 确保许可证包含所有V23字段

错误: "machine binding failed"
解决: 确认机器ID解密密钥正确
```

## 📞 **紧急支持**

如果更新后仍无法验证许可证，请立即联系并提供：
1. 完整的错误日志
2. 许可证文件内容 (隐藏敏感信息)
3. 使用的验证器版本

## 🎯 **更新总结**

**更新前**: 
- ❌ 无法验证V25许可证
- ❌ 签名密钥与机器密钥相同
- ❌ 不支持新许可证字段

**更新后**:
- ✅ 支持V25许可证验证
- ✅ 签名密钥与机器密钥分离
- ✅ 支持许可证类型和开始日期
- ✅ 增强的安全架构

**立即更新，确保软件正常运行！** 🚀
