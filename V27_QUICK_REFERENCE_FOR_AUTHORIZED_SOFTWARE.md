# V27 Quick Reference for Authorized Software
# V27被授权软件快速参考

## 🚨 BREAKING CHANGE ALERT / 重大变更警告

**V27 introduces Company ID support with new license format**
**V27引入公司ID支持和新许可证格式**

❌ **No backward compatibility** - V27 licenses cannot be validated by old validators
❌ **无向后兼容性** - V27许可证无法被旧验证器验证

## 📋 What's New in V27 / V27新功能

### New License Field / 新许可证字段
```json
{
  "encrypted_data_block": "base64_encrypted_company_id..."  // ← NEW: Hidden Company ID
}
```

### Updated Signature / 更新的签名
- Now includes Company ID hash in digital signature
- 现在在数字签名中包含公司ID哈希

### New Key Required / 需要新密钥
- **Company ID Decryption Key** (3rd key needed)
- **公司ID解密密钥**（需要第三个密钥）

## 🔑 Required Keys / 必需密钥

Your authorized software needs **THREE** public keys:
您的被授权软件需要**三个**公钥：

1. **Machine ID Decryption**: `MACHINE_ID_DECRYPTION_PUBLIC_KEY`
2. **Signature Verification**: `SIGNATURE_VERIFICATION_PUBLIC_KEY`  
3. **Company ID Decryption**: `COMPANY_ID_DECRYPTION_PUBLIC_KEY` ← **NEW**

## 📝 Code Changes Required / 需要的代码修改

### 1. Update License Structure / 更新许可证结构
```go
type LicenseData struct {
    // ... existing fields ...
    EncryptedDataBlock string `json:"encrypted_data_block"` // ← ADD THIS
    // ... rest of fields ...
}
```

### 2. Update Signature Structure / 更新签名结构
```go
type SignatureData struct {
    // ... existing fields ...
    CompanyIDHash string `json:"c"` // ← ADD THIS
    // ... rest of fields ...
}
```

### 3. Add Company ID Decryption / 添加公司ID解密
```go
// Decrypt company ID from encrypted_data_block
companyID, err := DecryptData(license.EncryptedDataBlock, companyIDKey)
```

### 4. Update Signature Verification / 更新签名验证
```go
sigData := SignatureData{
    // ... existing fields ...
    CompanyIDHash: hashString(companyID), // ← ADD THIS
}
```

## 🎯 Company ID Details / 公司ID详情

### Format / 格式
- **7 digits, no hyphen**: `1234567` (not `123-4567`)
- **Range**: `1000000` to `9999999`
- **Storage**: Only encrypted in license (no plaintext)

### Company Ranges / 公司范围
- `1000000-1999999`: General companies
- `2000000-2999999`: Premium partners  
- `3000000-3999999`: Government/Enterprise
- `9000000-9999999`: Testing/Special

### Usage Example / 使用示例
```go
// Enable features based on company ID
switch {
case strings.HasPrefix(companyID, "1"):
    enableStandardFeatures()
case strings.HasPrefix(companyID, "2"):
    enablePremiumFeatures()
case strings.HasPrefix(companyID, "3"):
    enableEnterpriseFeatures()
}
```

## 🔒 Security Features / 安全特性

### Field Name Obfuscation / 字段名混淆
- Field name: `encrypted_data_block` (purpose hidden)
- 字段名：`encrypted_data_block`（隐藏用途）

### No Plaintext Exposure / 无明文暴露
- Company ID never appears in plaintext in license
- 公司ID从不在许可证中以明文形式出现

### Enhanced Signature / 增强签名
- Signature includes company ID hash for integrity
- 签名包含公司ID哈希以确保完整性

## ⚡ Quick Implementation / 快速实现

### Step 1: Get Public Keys / 步骤1：获取公钥
Copy from `public_keys_for_authorized_software.go`
从`public_keys_for_authorized_software.go`复制

### Step 2: Update Structures / 步骤2：更新结构
Add `EncryptedDataBlock` and `CompanyIDHash` fields
添加`EncryptedDataBlock`和`CompanyIDHash`字段

### Step 3: Update Validation / 步骤3：更新验证
```go
// Complete validation flow
result, err := ValidateLicense("factory_license.json")
if err != nil {
    log.Fatal("License validation failed")
}

fmt.Printf("Company ID: %s\n", result.CompanyID)
EnableCompanyFeatures(result.CompanyID)
```

## 🧪 Testing Checklist / 测试检查清单

- [ ] Load V27 license file successfully
- [ ] Decrypt company ID from `encrypted_data_block`
- [ ] Verify signature including company ID hash
- [ ] Handle company-specific features
- [ ] Test with different company ID ranges
- [ ] Verify machine binding still works
- [ ] Check license expiration validation

## 📁 Files You Need / 您需要的文件

### Documentation / 文档
- `AUTHORIZED_SOFTWARE_INTEGRATION_GUIDE.md` - Complete guide
- `V27_QUICK_REFERENCE_FOR_AUTHORIZED_SOFTWARE.md` - This file

### Code Examples / 代码示例
- `license_validator_v27_with_company_id.go` - Complete validator
- `public_keys_for_authorized_software.go` - Required keys

### Sample License / 示例许可证
- `example_final_license_format.json` - V27 format example

## 🚨 Common Pitfalls / 常见陷阱

### ❌ Wrong Key Format
- Machine ID key: `RSA PUBLIC KEY` format
- Company ID key: `PUBLIC KEY` format (different!)

### ❌ Missing Company ID in Signature
- Must include `CompanyIDHash` in signature verification
- 必须在签名验证中包含`CompanyIDHash`

### ❌ Wrong Company ID Format
- Use 7 digits without hyphen: `1234567`
- Not with hyphen: `123-4567`

### ❌ Field Name Confusion
- License field: `encrypted_data_block` (not `encrypted_company_id`)
- 许可证字段：`encrypted_data_block`（不是`encrypted_company_id`）

## 🆘 Troubleshooting / 故障排除

### Signature Verification Fails / 签名验证失败
1. Check if company ID hash is included
2. Verify company ID format (7 digits, no hyphen)
3. Ensure correct signature verification key

### Company ID Decryption Fails / 公司ID解密失败
1. Check if using correct company ID decryption key
2. Verify `encrypted_data_block` field exists
3. Check key format (PUBLIC KEY vs RSA PUBLIC KEY)

### License Loading Fails / 许可证加载失败
1. Verify JSON format is correct
2. Check all required fields are present
3. Ensure file is V27 format (has `encrypted_data_block`)

## 📞 Support / 支持

For integration support:
- Review complete examples in provided files
- Test with sample V27 licenses
- Verify all three keys are correctly embedded
- Ensure signature verification includes company ID

集成支持：
- 查看提供文件中的完整示例
- 使用V27许可证样本测试
- 验证所有三个密钥都正确嵌入
- 确保签名验证包含公司ID

---

**Version**: V27 Quick Reference  
**Last Updated**: 2025-07-13  
**Status**: Production Ready  
**Compatibility**: V27 Only (No Backward Compatibility)
