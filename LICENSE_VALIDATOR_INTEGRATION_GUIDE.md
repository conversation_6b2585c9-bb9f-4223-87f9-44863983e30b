# License Validator Integration Guide
# 许可证验证器集成指南

## 📋 Overview / 概述

This guide helps you integrate the `standalone_license_validator.go` into your authorized software to validate license files.

本指南帮助您将 `standalone_license_validator.go` 集成到您的被授权软件中，以验证许可证文件。

## 🚀 Quick Start / 快速开始

### Step 1: Copy the Validator / 复制验证器
```bash
# Copy standalone_license_validator.go to your project
cp standalone_license_validator.go /path/to/your/project/
```

### Step 2: Modify Package Name / 修改包名
```go
// Change the first line from:
package main

// To your project package name:
package yourapp
```

### Step 3: Basic Integration / 基本集成
```go
package main

import (
    "log"
    "os"
)

func main() {
    // Validate license on startup
    err := ValidateLicenseFile("factory_license.json")
    if err != nil {
        log.Fatal("License validation failed:", err)
        // Exit the application
        os.Exit(1)
    }
    
    log.Println("License validated successfully!")
    // Continue with your application logic
    runYourApplication()
}

func runYourApplication() {
    // Your application code here
    log.Println("Application is running...")
}
```

## 🔧 Advanced Integration / 高级集成

### 1. Periodic License Checking / 定期许可证检查
```go
import (
    "time"
)

func startLicenseMonitor() {
    ticker := time.NewTicker(24 * time.Hour) // Check daily
    go func() {
        for {
            select {
            case <-ticker.C:
                err := ValidateLicenseFile("factory_license.json")
                if err != nil {
                    log.Printf("License validation failed: %v", err)
                    // Handle license failure (e.g., disable features)
                    handleLicenseFailure()
                }
            }
        }
    }()
}
```

### 2. Feature-Based Validation / 基于功能的验证
```go
func validateLicenseForFeature(featureName string) error {
    validator, err := NewLicenseValidator()
    if err != nil {
        return err
    }
    
    license, err := LoadLicenseFromFile("factory_license.json")
    if err != nil {
        return err
    }
    
    // Check if license covers this feature
    if !strings.Contains(license.AuthorizedSoftware, featureName) {
        return fmt.Errorf("feature '%s' not authorized", featureName)
    }
    
    return validator.ValidateLicense(license)
}

// Usage example
func criticalFunction() error {
    err := validateLicenseForFeature("LS-DYNA")
    if err != nil {
        return fmt.Errorf("feature not licensed: %v", err)
    }
    
    // Execute the critical function
    return nil
}
```

### 3. Custom License Path / 自定义许可证路径
```go
import (
    "flag"
    "path/filepath"
)

func main() {
    // Allow custom license file path
    licensePath := flag.String("license", "factory_license.json", "Path to license file")
    flag.Parse()
    
    // Make path absolute
    absPath, err := filepath.Abs(*licensePath)
    if err != nil {
        log.Fatal("Invalid license path:", err)
    }
    
    err = ValidateLicenseFile(absPath)
    if err != nil {
        log.Fatal("License validation failed:", err)
    }
    
    log.Printf("License validated: %s", absPath)
}
```

## ⚠️ Important Notes / 重要注意事项

### 1. Machine ID Implementation / 机器ID实现
**CRITICAL**: You must implement `getCurrentMachineID()` function to match your machine ID generation logic.

**关键**: 您必须实现 `getCurrentMachineID()` 函数以匹配您的机器ID生成逻辑。

```go
func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
    // TODO: Implement your machine ID logic here
    // This should return the SAME machine ID used when generating the license
    
    // Example implementation (customize as needed):
    cpuID := getCPUSerialNumber()
    diskID := getDiskSerialNumber()
    machineID := fmt.Sprintf("%s-%s-%s", cpuID, diskID, runtime.GOOS)
    
    return machineID, nil
}
```

### 2. Security Considerations / 安全考虑

#### Code Obfuscation / 代码混淆
```bash
# Use code obfuscation tools to protect embedded keys
go build -ldflags="-s -w" -o your_app
```

#### Key Protection / 密钥保护
- The embedded RSA private key should be protected
- Consider using code obfuscation tools
- Add anti-debugging measures if needed

嵌入的RSA私钥应该被保护
- 考虑使用代码混淆工具
- 如需要可添加反调试措施

### 3. Error Handling / 错误处理
```go
func handleLicenseError(err error) {
    switch {
    case strings.Contains(err.Error(), "expired"):
        showExpirationDialog()
    case strings.Contains(err.Error(), "machine binding"):
        showMachineBindingError()
    case strings.Contains(err.Error(), "signature"):
        showTamperingError()
    default:
        showGenericLicenseError()
    }
}
```

## 📁 File Structure / 文件结构

```
your_project/
├── main.go
├── license_validator.go          # Renamed from standalone_license_validator.go
├── factory_license.json          # License file from customer
└── config/
    └── app_config.go
```

## 🧪 Testing / 测试

### Unit Test Example / 单元测试示例
```go
func TestLicenseValidation(t *testing.T) {
    // Test with valid license
    err := ValidateLicenseFile("test_factory_license.json")
    if err != nil {
        t.Errorf("Valid license failed validation: %v", err)
    }
    
    // Test with expired license
    err = ValidateLicenseFile("expired_license.json")
    if err == nil {
        t.Error("Expired license should fail validation")
    }
}
```

## 🔍 Troubleshooting / 故障排除

### Common Issues / 常见问题

1. **Machine ID Mismatch / 机器ID不匹配**
   ```
   Error: license is not valid for this machine
   Solution: Ensure getCurrentMachineID() returns the same ID used during license generation
   ```

2. **Signature Verification Failed / 签名验证失败**
   ```
   Error: signature verification failed
   Solution: License file may be corrupted or tampered with
   ```

3. **License Expired / 许可证过期**
   ```
   Error: license has expired
   Solution: Contact license provider for renewal
   ```

### Debug Mode / 调试模式
```go
func enableDebugMode() {
    log.SetLevel(log.DebugLevel)
    log.Debug("License validation debug mode enabled")
}
```

## 📞 Support / 支持

If you encounter issues during integration, please check:
如果在集成过程中遇到问题，请检查：

1. License file format matches expected structure
2. Machine ID generation logic is consistent
3. All required fields are present in license
4. File permissions allow reading the license file

For additional support, contact the license provider with:
- Error messages
- License file (if not confidential)
- Machine information
- Integration code snippets

## 📚 Additional Resources / 其他资源

- `MACHINE_ID_IMPLEMENTATION_GUIDE.md` - Detailed machine ID implementation guide
- `example_integration.go` - Complete integration example
- `standalone_license_validator.go` - The main validator file to integrate

## 🎯 Best Practices / 最佳实践

1. **Validate Early**: Check license during application startup
2. **Fail Gracefully**: Provide clear error messages to users
3. **Secure Storage**: Protect the license file from tampering
4. **Regular Checks**: Periodically re-validate the license
5. **Logging**: Log license validation events for audit purposes

1. **早期验证**: 在应用程序启动时检查许可证
2. **优雅失败**: 向用户提供清晰的错误消息
3. **安全存储**: 保护许可证文件免受篡改
4. **定期检查**: 定期重新验证许可证
5. **日志记录**: 记录许可证验证事件以供审计
