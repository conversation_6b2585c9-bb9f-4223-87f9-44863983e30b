package main

import (
	"fmt"
)

func main() {
	fmt.Println("🔍 Testing Company ID Signature Behavior")
	fmt.Println("=========================================")

	// Test 1: Company ID Hash with Hyphen
	fmt.Println("\n📝 Test 1: Company ID Hash Generation")
	fmt.Println("-------------------------------------")

	testCompanyIDs := []string{
		"123-4567", // With hyphen (formatted)
		"1234567",  // Without hyphen (raw)
		"100-0000", // Starting ID with hyphen
		"1000000",  // Starting ID without hyphen
	}

	for _, companyID := range testCompanyIDs {
		hash := hashString(companyID)
		fmt.Printf("Company ID: %-10s → Hash: %s\n", companyID, hash)
	}

	// Test 2: Verify Different Hashes
	fmt.Println("\n🔍 Test 2: Hash Comparison")
	fmt.Println("--------------------------")

	withHyphen := hashString("123-4567")
	withoutHyphen := hashString("1234567")

	fmt.Printf("With hyphen:    %s\n", withHyphen)
	fmt.Printf("Without hyphen: %s\n", withoutHyphen)

	if withHyphen == withoutHyphen {
		fmt.Println("❌ ERROR: Hashes are the same! This should not happen.")
	} else {
		fmt.Println("✅ CORRECT: Hashes are different. Hyphen is included in signature.")
	}

	// Test 3: Signature Data Structure
	fmt.Println("\n📋 Test 3: Signature Data Structure")
	fmt.Println("-----------------------------------")

	// Create test signature data
	sigData := SignatureData{
		Software:       "Test Software",
		Version:        "1.0.0",
		LicenseType:    "lease",
		StartUnix:      **********, // 2024-01-13
		ExpirationUnix: **********, // 2025-01-13
		MachineIDHash:  hashString("test-machine-id"),
		CompanyIDHash:  hashString("123-4567"), // With hyphen
	}

	fmt.Printf("Software: %s\n", sigData.Software)
	fmt.Printf("Version: %s\n", sigData.Version)
	fmt.Printf("License Type: %s\n", sigData.LicenseType)
	fmt.Printf("Start Unix: %d\n", sigData.StartUnix)
	fmt.Printf("Expiration Unix: %d\n", sigData.ExpirationUnix)
	fmt.Printf("Machine ID Hash: %s\n", sigData.MachineIDHash)
	fmt.Printf("Company ID Hash: %s\n", sigData.CompanyIDHash)

	fmt.Println("\n🎯 Summary")
	fmt.Println("----------")
	fmt.Println("✅ Company ID signature includes the hyphen character")
	fmt.Println("✅ Format '123-4567' is used in signature, not '1234567'")
	fmt.Println("✅ This ensures signature validation requires exact format match")
	fmt.Println("✅ License validators must use the same formatted company ID")

	fmt.Println("\n📋 Implementation Notes")
	fmt.Println("-----------------------")
	fmt.Println("1. Company ID is stored in formatted form (123-4567) in registry")
	fmt.Println("2. Signature uses the formatted company ID with hyphen")
	fmt.Println("3. License file contains only encrypted company ID (no plaintext)")
	fmt.Println("4. Validators must decrypt company ID and use formatted version for verification")
}
