// simple_key_check.go
// 简单检查四个密钥文件

package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"math/big"
	"os"
)

func main() {
	fmt.Println("🔍 Simple Key Separation Check")
	fmt.Println("==============================")

	// 加载四个密钥文件
	machinePriv, err := loadPrivKey("machine_id_decryption_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Machine private key error: %v\n", err)
		return
	}

	machinePub, err := loadPubKey("machine_id_decryption_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Machine public key error: %v\n", err)
		return
	}

	sigPriv, err := loadPrivKey("signature_generation_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Signature private key error: %v\n", err)
		return
	}

	sigPub, err := loadPubKey("signature_verification_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Signature public key error: %v\n", err)
		return
	}

	fmt.Println("✅ All four keys loaded")

	// 检查关键问题：机器私钥是否与签名公钥配对
	machinePrivPub := &machinePriv.PublicKey
	
	fmt.Println("\n🔍 Critical Check:")
	fmt.Printf("Machine Private Key N: %s\n", getFingerprint(machinePriv.N))
	fmt.Printf("Signature Public Key N: %s\n", getFingerprint(sigPub.N))
	
	if machinePrivPub.N.Cmp(sigPub.N) == 0 {
		fmt.Println("❌ PROBLEM: Machine private key ↔ Signature public key are SAME pair!")
		fmt.Println("   被授权软件反馈正确: 不是两对不同的密钥")
	} else {
		fmt.Println("✅ GOOD: Machine private key ↔ Signature public key are DIFFERENT pairs")
		fmt.Println("   密钥分离成功")
	}

	// 检查其他组合
	fmt.Println("\n📋 All Combinations:")
	fmt.Printf("Machine Priv ↔ Machine Pub: %s\n", checkMatch(machinePriv.N, machinePub.N))
	fmt.Printf("Signature Priv ↔ Signature Pub: %s\n", checkMatch(sigPriv.N, sigPub.N))
	fmt.Printf("Machine Priv ↔ Signature Pub: %s\n", checkMatch(machinePriv.N, sigPub.N))
	fmt.Printf("Signature Priv ↔ Machine Pub: %s\n", checkMatch(sigPriv.N, machinePub.N))
}

func loadPrivKey(filename string) (*rsa.PrivateKey, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, fmt.Errorf("no PEM block")
	}
	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

func loadPubKey(filename string) (*rsa.PublicKey, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, fmt.Errorf("no PEM block")
	}
	return x509.ParsePKCS1PublicKey(block.Bytes)
}

func getFingerprint(n *big.Int) string {
	bytes := n.Bytes()
	if len(bytes) >= 4 {
		return fmt.Sprintf("%x", bytes[:4])
	}
	return fmt.Sprintf("%x", bytes)
}

func checkMatch(n1, n2 *big.Int) string {
	if n1.Cmp(n2) == 0 {
		return "SAME ❌"
	}
	return "DIFFERENT ✅"
}
