# Machine Info vs Signature Keys Analysis
# 机器信息与签名密钥使用分析

## 🔍 **关键问题回答**

**程序中用的加解密机器信息的密钥，与用来加密签名的密钥是相同的吗？**

**答案：在V25版本中，这两个密钥已经分离，不再相同！**

## 📋 **详细分析**

### 🔐 **当前V25架构中的密钥使用**

#### **1. 机器信息加解密密钥**
```go
// 在 license.go 中
rawMachineID, err := GetRawMachineID(lg.machineInfo, lg.machineDecryptionKey)

// 使用的密钥文件
machineDecryptionKeyPath := "machine_decryption_private_key_to_decryp_factory_machineinfo.pem"
```

**用途**: 解密机器绑定信息
**密钥指纹**: `zMPjnGYh5C7HVbasl68s...` (旧密钥)

#### **2. 数字签名密钥**
```go
// 在 license.go 中
signature, err := CreateSignature(license, lg.signingKey, licenseStartTime, licenseExpirationTime, rawMachineID)

// 使用的密钥文件
signingKeyPath := "license_signing_private_key.pem"
```

**用途**: 创建数字签名
**密钥指纹**: `yaUiwY/7/jlelAe8XQOA...` (新密钥)

### 🎯 **密钥分离实现**

#### **LicenseGenerator结构体**
```go
type LicenseGenerator struct {
    machineInfo          *MachineInfo
    machineDecryptionKey *rsa.PrivateKey  // 机器信息解密密钥
    signingKey           *rsa.PrivateKey  // 数字签名密钥
}
```

#### **构造函数调用**
```go
generator, err := NewLicenseGenerator(
    machineInfoEntry.Text,             // 机器信息文件
    privateKeyEntry.Text,              // 机器解密密钥 (旧密钥)
    "license_signing_private_key.pem", // 签名密钥 (新密钥)
)
```

## 📊 **密钥使用对比**

### **V24及之前版本 (单一密钥)**
```
RSA密钥对 (zMPjnGYh5C7HVbasl68s...)
├── 私钥用途:
│   ├── 机器信息解密 ✓
│   └── 数字签名创建 ✓
└── 公钥用途:
    ├── 机器信息加密 ✓
    └── 签名验证 ✓

结果: 相同密钥用于两种用途
```

### **V25版本 (分离密钥)**
```
机器绑定密钥对 (zMPjnGYh5C7HVbasl68s...)
├── 私钥: 机器信息解密 ✓
└── 公钥: 机器信息加密 ✓

数字签名密钥对 (yaUiwY/7/jlelAe8XQOA...)
├── 私钥: 数字签名创建 ✓
└── 公钥: 签名验证 ✓

结果: 不同密钥用于不同用途
```

## 🔧 **实际代码验证**

### **机器信息解密流程**
```go
// 步骤1: 加载机器解密密钥
machineDecryptionKey, err := LoadPrivateKey(machineDecryptionKeyPath)

// 步骤2: 解密机器ID
rawMachineID, err := GetRawMachineID(lg.machineInfo, lg.machineDecryptionKey)

// 步骤3: 在GetRawMachineID函数中
decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, decodedData, nil)
```

### **数字签名创建流程**
```go
// 步骤1: 加载签名密钥
signingKey, err := LoadPrivateKey(signingKeyPath)

// 步骤2: 创建签名
signature, err := CreateSignature(license, lg.signingKey, startTime, endTime, rawMachineID)

// 步骤3: 在CreateSignature函数中
signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
```

## 🔒 **安全性分析**

### **密钥分离的安全优势**

| 场景 | 单一密钥影响 | 分离密钥影响 |
|------|-------------|-------------|
| **机器密钥泄露** | ❌ 机器绑定+签名都失效 | ⚠️ 仅机器绑定失效 |
| **签名密钥泄露** | ❌ 机器绑定+签名都失效 | ⚠️ 仅签名完整性失效 |
| **部分恢复** | ❌ 必须全部重新部署 | ✅ 可以部分恢复 |

### **风险隔离效果**
- ✅ **机器绑定独立**: 签名密钥泄露不影响机器绑定
- ✅ **签名独立**: 机器密钥泄露不影响签名完整性
- ✅ **渐进恢复**: 可以单独更换受影响的密钥

## 📁 **文件结构对比**

### **当前配置文件**
```json
{
  "version": "v23.0",
  "machine_info_path": "factory_machine_info.json",
  "private_key_path": "machine_decryption_private_key_to_decryp_factory_machineinfo.pem"
}
```

### **实际使用的密钥文件**
```
机器信息解密:
└── machine_decryption_private_key_to_decryp_factory_machineinfo.pem

数字签名创建:
└── license_signing_private_key.pem (硬编码路径)
```

## 🎯 **关键发现**

### **密钥使用现状**
1. **机器信息解密**: 使用旧密钥 (`zMPjnGYh5C7HVbasl68s...`)
2. **数字签名**: 使用新密钥 (`yaUiwY/7/jlelAe8XQOA...`)
3. **密钥分离**: ✅ 已实现
4. **向后兼容**: ✅ 机器绑定保持不变

### **安全改进**
- ✅ **职责分离**: 不同功能使用不同密钥
- ✅ **风险隔离**: 部分密钥泄露不会导致全系统失效
- ✅ **最小权限**: 每个密钥只有特定用途的权限

## 📋 **验证方法**

### **检查密钥是否相同**
```bash
# 比较机器解密密钥
head -2 machine_decryption_private_key_to_decryp_factory_machineinfo.pem

# 比较签名密钥
head -2 license_signing_private_key.pem

# 结果: 密钥开头不同，确认是不同的密钥对
```

### **代码中的使用确认**
```go
// 机器解密使用
lg.machineDecryptionKey  // 旧密钥

// 签名创建使用  
lg.signingKey           // 新密钥

// 确认: 使用不同的密钥变量
```

## 🎉 **结论**

**在V25版本中，程序使用的加解密机器信息的密钥与用来创建签名的密钥已经完全分离：**

- ❌ **不再相同**: 两个功能使用不同的RSA密钥对
- ✅ **安全提升**: 实现了密钥职责分离
- ✅ **风险隔离**: 部分密钥泄露不会导致全系统失效
- ✅ **向后兼容**: 机器绑定功能保持不变

**这是一个重要的安全改进，符合密码学最佳实践！** 🔐
