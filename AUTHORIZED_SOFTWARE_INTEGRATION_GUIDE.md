# Authorized Software Integration Guide for Company ID
# 被授权软件公司ID集成指南

## Overview / 概述

This guide explains how authorized software should integrate with the new Company ID feature in V27 license format. The license now includes an encrypted company ID field that requires proper validation and extraction.

本指南说明被授权软件如何与V27许可证格式中的新公司ID功能集成。许可证现在包含一个加密的公司ID字段，需要正确的验证和提取。

## New License Format / 新许可证格式

### V27 License Structure / V27许可证结构

```json
{
  "company_name": "Example Company",
  "email": "<EMAIL>",
  "phone": "18888888888",
  "authorized_software": "Your Software Name",
  "authorized_version": "1.0.0",
  "license_type": "lease",
  "start_date": "2025-07-13",
  "expiration_date": "2026-01-13",
  "issued_date": "2025-07-13",
  "encrypted_data_block": "base64_encrypted_company_id...",  // ← NEW: Encrypted Company ID
  "encrypted_machine_id": "base64_encrypted_machine_id...",
  "signature": "base64_digital_signature..."                 // ← UPDATED: Includes Company ID hash
}
```

### Key Changes / 关键变更

1. **New Field**: `encrypted_data_block` contains encrypted 7-digit company ID
2. **Updated Signature**: Digital signature now includes company ID hash
3. **No Plaintext**: Company ID is never stored in plaintext
4. **Format**: Company ID is 7 digits without hyphen (e.g., `1234567`)

1. **新字段**：`encrypted_data_block`包含加密的7位公司ID
2. **更新签名**：数字签名现在包含公司ID哈希
3. **无明文**：公司ID从不以明文形式存储
4. **格式**：公司ID是7位数字，无短横线（例如：`1234567`）

## Required Keys / 必需密钥

### Three Key System / 三密钥系统

Your authorized software needs access to three RSA public keys:

您的被授权软件需要访问三个RSA公钥：

1. **Machine ID Decryption Key**: `machine_id_decryption_public_key.pem`
2. **Signature Verification Key**: `signature_verification_public_key.pem`
3. **Company ID Decryption Key**: `company_id_decryption_public_key.pem` (NEW)

### Key Files to Embed / 需要嵌入的密钥文件

```go
// Embed these public keys in your authorized software
const MACHINE_ID_DECRYPTION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`

const SIGNATURE_VERIFICATION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`

const COMPANY_ID_DECRYPTION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----  // ← NEW
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`
```

## Implementation Steps / 实现步骤

### Step 1: Update License Data Structure / 步骤1：更新许可证数据结构

```go
// Update your license data structure
type LicenseData struct {
    CompanyName        string `json:"company_name"`
    Email              string `json:"email"`
    Phone              string `json:"phone"`
    AuthorizedSoftware string `json:"authorized_software"`
    AuthorizedVersion  string `json:"authorized_version"`
    LicenseType        string `json:"license_type"`
    StartDate          string `json:"start_date"`
    ExpirationDate     string `json:"expiration_date"`
    IssuedDate         string `json:"issued_date"`
    EncryptedDataBlock string `json:"encrypted_data_block"` // ← NEW: Encrypted Company ID
    EncryptedMachineID string `json:"encrypted_machine_id"`
    Signature          string `json:"signature"`
}
```

### Step 2: Update Signature Data Structure / 步骤2：更新签名数据结构

```go
// Update signature verification structure
type SignatureData struct {
    Software       string `json:"s"` // Software name (shortened key)
    Version        string `json:"v"` // Software version (shortened key)
    LicenseType    string `json:"t"` // License type (shortened key)
    StartUnix      int64  `json:"b"` // Start date as Unix timestamp
    ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp
    MachineIDHash  string `json:"m"` // Hash of machine ID
    CompanyIDHash  string `json:"c"` // ← NEW: Hash of company ID
}
```

### Step 3: Add Company ID Decryption Function / 步骤3：添加公司ID解密函数

```go
// DecryptCompanyID decrypts the company ID from encrypted_data_block
func DecryptCompanyID(encryptedDataBlock string, publicKey *rsa.PublicKey) (string, error) {
    // Decode base64
    encryptedData, err := base64.StdEncoding.DecodeString(encryptedDataBlock)
    if err != nil {
        return "", fmt.Errorf("failed to decode encrypted data block: %v", err)
    }
    
    // Decrypt using company ID decryption key
    decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, publicKey, encryptedData, nil)
    if err != nil {
        return "", fmt.Errorf("failed to decrypt company ID: %v", err)
    }
    
    return string(decryptedData), nil
}
```

### Step 4: Update Signature Verification / 步骤4：更新签名验证

```go
// VerifyLicenseSignature verifies the license signature including company ID
func VerifyLicenseSignature(license *LicenseData, machineID, companyID string, publicKey *rsa.PublicKey) error {
    // Parse dates
    startTime, err := time.Parse("2006-01-02", license.StartDate)
    if err != nil {
        return fmt.Errorf("failed to parse start date: %v", err)
    }
    
    expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
    if err != nil {
        return fmt.Errorf("failed to parse expiration date: %v", err)
    }
    
    // Create signature data (must match generator format)
    sigData := SignatureData{
        Software:       license.AuthorizedSoftware,
        Version:        license.AuthorizedVersion,
        LicenseType:    license.LicenseType,
        StartUnix:      startTime.Unix(),
        ExpirationUnix: expirationTime.Unix(),
        MachineIDHash:  hashString(machineID),
        CompanyIDHash:  hashString(companyID), // ← NEW: Include company ID hash
    }
    
    // Convert to JSON
    jsonData, err := json.Marshal(sigData)
    if err != nil {
        return fmt.Errorf("failed to marshal signature data: %v", err)
    }
    
    // Create hash
    hash := sha256.Sum256(jsonData)
    
    // Decode signature
    signature, err := base64.StdEncoding.DecodeString(license.Signature)
    if err != nil {
        return fmt.Errorf("failed to decode signature: %v", err)
    }
    
    // Verify signature
    err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
    if err != nil {
        return fmt.Errorf("signature verification failed: %v", err)
    }
    
    return nil
}
```

### Step 5: Complete License Validation / 步骤5：完整许可证验证

```go
// ValidateLicense performs complete license validation including company ID
func ValidateLicense(licenseFile string) (*LicenseValidationResult, error) {
    // 1. Load and parse license
    license, err := LoadLicense(licenseFile)
    if err != nil {
        return nil, fmt.Errorf("failed to load license: %v", err)
    }
    
    // 2. Decrypt machine ID
    machineIDKey, err := LoadPublicKey(MACHINE_ID_DECRYPTION_PUBLIC_KEY)
    if err != nil {
        return nil, fmt.Errorf("failed to load machine ID key: %v", err)
    }
    
    machineID, err := DecryptMachineID(license.EncryptedMachineID, machineIDKey)
    if err != nil {
        return nil, fmt.Errorf("failed to decrypt machine ID: %v", err)
    }
    
    // 3. Decrypt company ID (NEW)
    companyIDKey, err := LoadPublicKey(COMPANY_ID_DECRYPTION_PUBLIC_KEY)
    if err != nil {
        return nil, fmt.Errorf("failed to load company ID key: %v", err)
    }
    
    companyID, err := DecryptCompanyID(license.EncryptedDataBlock, companyIDKey)
    if err != nil {
        return nil, fmt.Errorf("failed to decrypt company ID: %v", err)
    }
    
    // 4. Verify signature (including company ID)
    signatureKey, err := LoadPublicKey(SIGNATURE_VERIFICATION_PUBLIC_KEY)
    if err != nil {
        return nil, fmt.Errorf("failed to load signature key: %v", err)
    }
    
    err = VerifyLicenseSignature(license, machineID, companyID, signatureKey)
    if err != nil {
        return nil, fmt.Errorf("signature verification failed: %v", err)
    }
    
    // 5. Validate machine binding
    currentMachineID := GetCurrentMachineID()
    if machineID != currentMachineID {
        return nil, fmt.Errorf("license is not valid for this machine")
    }
    
    // 6. Check expiration
    expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
    if err != nil {
        return nil, fmt.Errorf("failed to parse expiration date: %v", err)
    }
    
    if time.Now().After(expirationTime) {
        return nil, fmt.Errorf("license has expired")
    }
    
    // Return validation result with company ID
    return &LicenseValidationResult{
        Valid:     true,
        CompanyID: companyID,        // ← NEW: Return decrypted company ID
        MachineID: machineID,
        License:   license,
    }, nil
}
```

## Usage Examples / 使用示例

### Basic Validation / 基本验证

```go
func main() {
    // Validate license and get company ID
    result, err := ValidateLicense("factory_license.json")
    if err != nil {
        log.Fatalf("License validation failed: %v", err)
    }
    
    fmt.Printf("License is valid!\n")
    fmt.Printf("Company ID: %s\n", result.CompanyID)        // ← NEW: Access company ID
    fmt.Printf("Company Name: %s\n", result.License.CompanyName)
    fmt.Printf("License Type: %s\n", result.License.LicenseType)
    fmt.Printf("Expires: %s\n", result.License.ExpirationDate)
}
```

### Company-Specific Features / 公司特定功能

```go
// Enable features based on company ID
func EnableCompanyFeatures(companyID string) {
    switch {
    case strings.HasPrefix(companyID, "1"):
        // General companies (1000000-1999999)
        enableStandardFeatures()
    case strings.HasPrefix(companyID, "2"):
        // Premium partners (2000000-2999999)
        enablePremiumFeatures()
    case strings.HasPrefix(companyID, "3"):
        // Government/Enterprise (3000000-3999999)
        enableEnterpriseFeatures()
    default:
        enableBasicFeatures()
    }
}
```

## Migration Guide / 迁移指南

### From V26 to V27 / 从V26到V27

1. **Add Company ID Support**: Update license validation to handle `encrypted_data_block`
2. **Update Signature Verification**: Include company ID hash in signature validation
3. **Add New Key**: Embed company ID decryption public key
4. **Handle Backward Compatibility**: Detect and handle old license formats if needed

1. **添加公司ID支持**：更新许可证验证以处理`encrypted_data_block`
2. **更新签名验证**：在签名验证中包含公司ID哈希
3. **添加新密钥**：嵌入公司ID解密公钥
4. **处理向后兼容性**：如需要，检测和处理旧许可证格式

### Compatibility Detection / 兼容性检测

```go
// DetectLicenseVersion detects license format version
func DetectLicenseVersion(license *LicenseData) string {
    if license.EncryptedDataBlock != "" {
        return "V27" // Has encrypted company ID
    }
    return "V26"     // Legacy format
}
```

## Security Considerations / 安全考虑

### Key Management / 密钥管理

1. **Separate Keys**: Use separate keys for different purposes
2. **Key Rotation**: Plan for key rotation if needed
3. **Secure Storage**: Store keys securely in your application
4. **Access Control**: Limit access to decryption functions

1. **分离密钥**：为不同目的使用分离的密钥
2. **密钥轮换**：如需要，计划密钥轮换
3. **安全存储**：在应用程序中安全存储密钥
4. **访问控制**：限制对解密函数的访问

### Validation Best Practices / 验证最佳实践

1. **Complete Validation**: Always validate all components (signature, machine ID, company ID, expiration)
2. **Error Handling**: Provide clear error messages for different failure types
3. **Logging**: Log validation attempts for security monitoring
4. **Rate Limiting**: Implement rate limiting for validation attempts

1. **完整验证**：始终验证所有组件（签名、机器ID、公司ID、到期时间）
2. **错误处理**：为不同失败类型提供清晰的错误消息
3. **日志记录**：记录验证尝试以进行安全监控
4. **速率限制**：为验证尝试实施速率限制

## Testing / 测试

### Test Cases / 测试用例

1. **Valid License**: Test with valid V27 license
2. **Invalid Signature**: Test with tampered signature
3. **Wrong Machine**: Test on different machine
4. **Expired License**: Test with expired license
5. **Invalid Company ID**: Test with corrupted encrypted_data_block

1. **有效许可证**：使用有效的V27许可证测试
2. **无效签名**：使用被篡改的签名测试
3. **错误机器**：在不同机器上测试
4. **过期许可证**：使用过期许可证测试
5. **无效公司ID**：使用损坏的encrypted_data_block测试

## Support / 支持

For integration support:
- Review the complete license validator example
- Test with sample V27 licenses
- Verify all three keys are correctly embedded
- Ensure signature verification includes company ID hash

集成支持：
- 查看完整的许可证验证器示例
- 使用V27许可证样本测试
- 验证所有三个密钥都正确嵌入
- 确保签名验证包含公司ID哈希

---

**Version**: V27 Integration Guide  
**Last Updated**: 2025-07-13  
**Compatibility**: V27 License Format Only
