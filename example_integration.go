// example_integration.go
// 这是一个完整的示例，展示如何将license验证器集成到被授权软件中

package main

import (
	"fmt"
	"log"
	"os"
	"time"
)

// 这里应该包含从standalone_license_validator.go复制的所有代码
// 为了演示，我们只显示主要的集成逻辑

// 示例应用程序结构
type AuthorizedApp struct {
	name           string
	version        string
	licenseFile    string
	isLicenseValid bool
}

// 创建新的被授权应用程序实例
func NewAuthorizedApp(name, version, licenseFile string) *AuthorizedApp {
	return &AuthorizedApp{
		name:        name,
		version:     version,
		licenseFile: licenseFile,
	}
}

// 启动应用程序（包含许可证验证）
func (app *AuthorizedApp) Start() error {
	fmt.Printf("Starting %s v%s...\n", app.name, app.version)

	// 第1步：验证许可证
	fmt.Println("🔍 Validating license...")
	err := app.validateLicense()
	if err != nil {
		return fmt.Errorf("license validation failed: %v", err)
	}

	fmt.Println("✅ License validation successful!")
	app.isLicenseValid = true

	// 第2步：启动许可证监控
	app.startLicenseMonitor()

	// 第3步：运行主应用程序逻辑
	return app.runMainApplication()
}

// 验证许可证
func (app *AuthorizedApp) validateLicense() error {
	// 检查许可证文件是否存在
	if _, err := os.Stat(app.licenseFile); os.IsNotExist(err) {
		return fmt.Errorf("license file not found: %s", app.licenseFile)
	}

	// 使用standalone_license_validator.go中的函数
	// 注意：这里需要将standalone_license_validator.go的内容复制到项目中
	err := ValidateLicenseFile(app.licenseFile)
	if err != nil {
		return err
	}

	return nil
}

// 启动许可证监控（定期检查）
func (app *AuthorizedApp) startLicenseMonitor() {
	go func() {
		ticker := time.NewTicker(24 * time.Hour) // 每24小时检查一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				fmt.Println("🔄 Performing periodic license check...")
				err := app.validateLicense()
				if err != nil {
					log.Printf("⚠️ Periodic license check failed: %v", err)
					app.handleLicenseFailure(err)
				} else {
					fmt.Println("✅ Periodic license check passed")
				}
			}
		}
	}()
}

// 处理许可证验证失败
func (app *AuthorizedApp) handleLicenseFailure(err error) {
	app.isLicenseValid = false

	// 根据错误类型采取不同的行动
	errorMsg := err.Error()
	switch {
	case contains(errorMsg, "expired"):
		fmt.Println("❌ License has expired. Please contact your license provider for renewal.")
		app.showExpirationDialog()
	case contains(errorMsg, "machine binding"):
		fmt.Println("❌ License is not valid for this machine. Please contact support.")
		app.showMachineBindingError()
	case contains(errorMsg, "signature"):
		fmt.Println("❌ License file appears to be corrupted or tampered with.")
		app.showTamperingError()
	default:
		fmt.Printf("❌ License validation error: %v\n", err)
		app.showGenericError()
	}

	// 可选：优雅地关闭应用程序
	// os.Exit(1)
}

// 运行主应用程序逻辑
func (app *AuthorizedApp) runMainApplication() error {
	fmt.Printf("🚀 %s is now running...\n", app.name)

	// 模拟应用程序的主要功能
	for i := 0; i < 10; i++ {
		// 在执行关键功能前检查许可证状态
		if !app.isLicenseValid {
			return fmt.Errorf("cannot continue: license is invalid")
		}

		// 执行一些关键功能
		err := app.executeCriticalFunction(fmt.Sprintf("Task %d", i+1))
		if err != nil {
			log.Printf("Error executing task: %v", err)
			continue
		}

		time.Sleep(2 * time.Second) // 模拟工作
	}

	fmt.Println("✅ Application completed successfully!")
	return nil
}

// 执行关键功能（需要许可证验证）
func (app *AuthorizedApp) executeCriticalFunction(taskName string) error {
	// 在执行关键功能前再次验证许可证
	if !app.isLicenseValid {
		return fmt.Errorf("feature not licensed")
	}

	fmt.Printf("  ⚙️ Executing %s...\n", taskName)

	// 这里是您的实际业务逻辑
	// 例如：LS-DYNA计算、CAE分析等

	return nil
}

// 错误处理函数
func (app *AuthorizedApp) showExpirationDialog() {
	fmt.Println("📅 License Expiration Notice:")
	fmt.Println("   Your license has expired.")
	fmt.Println("   Please contact your license provider to renew.")
	fmt.Println("   Email: <EMAIL>")
}

func (app *AuthorizedApp) showMachineBindingError() {
	fmt.Println("🖥️ Machine Binding Error:")
	fmt.Println("   This license is not valid for this computer.")
	fmt.Println("   Please ensure you're using the correct license file.")
	fmt.Println("   Contact support if you believe this is an error.")
}

func (app *AuthorizedApp) showTamperingError() {
	fmt.Println("🔒 License Integrity Error:")
	fmt.Println("   The license file appears to be corrupted or modified.")
	fmt.Println("   Please obtain a new license file from your provider.")
}

func (app *AuthorizedApp) showGenericError() {
	fmt.Println("❓ License Validation Error:")
	fmt.Println("   An unexpected error occurred during license validation.")
	fmt.Println("   Please contact technical support for assistance.")
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr || 
		   len(s) > len(substr) && s[:len(substr)] == substr ||
		   len(s) > len(substr) && s[len(s)/2-len(substr)/2:len(s)/2+len(substr)/2] == substr
}

// 主函数 - 应用程序入口点
func main() {
	// 创建被授权应用程序实例
	app := NewAuthorizedApp("LS-DYNA Solver", "R13.1.1", "factory_license.json")

	// 启动应用程序
	err := app.Start()
	if err != nil {
		log.Fatal("Application failed to start:", err)
	}
}

/*
=== 集成检查清单 ===

✅ 必须完成的步骤：
1. 将standalone_license_validator.go复制到项目中
2. 修改package名称为您的项目包名
3. 实现getCurrentMachineID()函数以匹配机器ID生成逻辑
4. 在应用程序启动时调用license验证
5. 处理各种验证失败情况

✅ 推荐的增强功能：
1. 定期许可证检查
2. 在关键功能执行前验证许可证
3. 优雅的错误处理和用户提示
4. 日志记录用于审计
5. 配置文件支持自定义许可证路径

✅ 安全考虑：
1. 使用代码混淆保护嵌入的密钥
2. 添加反调试措施（如需要）
3. 保护许可证文件免受篡改
4. 定期检查许可证完整性

=== 技术支持 ===
如果在集成过程中遇到问题，请提供：
- 错误消息的完整文本
- 许可证文件格式（如果不涉及机密）
- 机器信息和环境详情
- 集成代码片段

联系方式：请通过许可证提供商获取技术支持
*/
