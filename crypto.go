package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"os"
	"time"
)

// LoadPrivateKey loads an RSA private key from a PEM file
func LoadPrivateKey(filePath string) (*rsa.PrivateKey, error) {
	keyData, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read private key file: %v", err)
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return privateKey, nil
}

// LoadMachineInfo loads machine information from JSON file
func LoadMachineInfo(filePath string) (*MachineInfo, error) {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read machine info file: %v", err)
	}

	var machineInfo MachineInfo
	err = json.Unmarshal(data, &machineInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to parse machine info JSON: %v", err)
	}

	return &machineInfo, nil
}

// GetRawMachineID extracts the raw machine ID, decrypting if necessary
func GetRawMachineID(machineInfo *MachineInfo, privateKey *rsa.PrivateKey) (string, error) {
	machineID := machineInfo.MachineID

	// Check if MachineID is already encrypted (Base64 format)
	if decodedData, err := base64.StdEncoding.DecodeString(machineID); err == nil && len(decodedData) > 100 {
		// Looks like encrypted data, try to decrypt
		decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, decodedData, nil)
		if err != nil {
			// If decryption fails, assume it's raw machine ID
			return machineID, nil
		}
		return string(decryptedData), nil
	}

	// Not encrypted, return as-is
	return machineID, nil
}

// DecryptMachineID decrypts the encrypted MachineID using the private key
func DecryptMachineID(encryptedMachineID string, privateKey *rsa.PrivateKey) (string, error) {
	// Decode base64 encrypted data
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 encrypted machine ID: %v", err)
	}

	// Decrypt using RSA OAEP
	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	return string(decryptedData), nil
}

// EncryptMachineID encrypts the machine ID using RSA public key
func EncryptMachineID(machineID string, privateKey *rsa.PrivateKey) (string, error) {
	publicKey := &privateKey.PublicKey

	encryptedData, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, []byte(machineID), nil)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt machine ID: %v", err)
	}

	return base64.StdEncoding.EncodeToString(encryptedData), nil
}

// GetEncryptedMachineID simply returns the encrypted MachineID from machine info
// This allows users to compare it with their machine info file for verification
func GetEncryptedMachineID(machineInfo *MachineInfo) string {
	return machineInfo.MachineID // Direct copy of encrypted MachineID
}

// CreateSignature creates a digital signature for the license data
// Note: Company name, email, and phone are NOT included in signature verification
func CreateSignature(licenseData *LicenseData, privateKey *rsa.PrivateKey, startTime, expirationTime time.Time, machineID, companyID string) (string, error) {
	// Create compact signature data to fit within RSA2048 limits
	// Excludes company name, email, and phone from signature verification
	sigData := SignatureData{
		Software:       licenseData.AuthorizedSoftware, // Software name only
		Version:        licenseData.AuthorizedVersion,  // Software version only
		LicenseType:    licenseData.LicenseType,        // License type
		StartUnix:      startTime.Unix(),               // Start date as Unix timestamp
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(machineID), // Use provided machine ID
		CompanyIDHash:  hashString(companyID), // Use provided company ID (includes hyphen, e.g., "123-4567")
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// Create hash of the data
	hash := sha256.Sum256(jsonData)

	// Sign the hash
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to create signature: %v", err)
	}

	// Return base64 encoded signature
	return base64.StdEncoding.EncodeToString(signature), nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// SaveLicenseToFile saves the license data to a JSON file
func SaveLicenseToFile(license *LicenseData, filePath string) error {
	jsonData, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal license data: %v", err)
	}

	// Check if file exists before writing
	if _, err := os.Stat(filePath); err == nil {
		// File exists, remove it first to ensure clean write
		err = os.Remove(filePath)
		if err != nil {
			return fmt.Errorf("failed to remove existing file: %v", err)
		}
	}

	err = ioutil.WriteFile(filePath, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("failed to write license file: %v", err)
	}

	// Verify the file was written correctly
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file was not created successfully: %s", filePath)
	}

	return nil
}

// ValidateFiles checks if the required files exist and are accessible
func ValidateFiles(machineInfoPath, privateKeyPath string) error {
	if _, err := os.Stat(machineInfoPath); os.IsNotExist(err) {
		return fmt.Errorf("machine info file does not exist: %s", machineInfoPath)
	}

	if _, err := os.Stat(privateKeyPath); os.IsNotExist(err) {
		return fmt.Errorf("private key file does not exist: %s", privateKeyPath)
	}

	return nil
}

// LoadCompanyIDPrivateKey loads the RSA private key for company ID encryption
func LoadCompanyIDPrivateKey(filePath string) (*rsa.PrivateKey, error) {
	return LoadPrivateKey(filePath) // Reuse existing function
}

// EncryptCompanyID encrypts the company ID using RSA public key
func EncryptCompanyID(companyID string, privateKey *rsa.PrivateKey) (string, error) {
	publicKey := &privateKey.PublicKey

	encryptedData, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, []byte(companyID), nil)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt company ID: %v", err)
	}

	return base64.StdEncoding.EncodeToString(encryptedData), nil
}

// GenerateCompanyIDKeys generates a new RSA key pair for company ID encryption
func GenerateCompanyIDKeys() error {
	// Generate private key
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return fmt.Errorf("failed to generate private key: %v", err)
	}

	// Save private key
	privateKeyPEM := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}

	privateKeyFile, err := os.Create("company_id_encryption_private_key.pem")
	if err != nil {
		return fmt.Errorf("failed to create private key file: %v", err)
	}
	defer privateKeyFile.Close()

	if err := pem.Encode(privateKeyFile, privateKeyPEM); err != nil {
		return fmt.Errorf("failed to write private key: %v", err)
	}

	// Save public key
	publicKeyPKIX, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return fmt.Errorf("failed to marshal public key: %v", err)
	}

	publicKeyPEM := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyPKIX,
	}

	publicKeyFile, err := os.Create("company_id_encryption_public_key.pem")
	if err != nil {
		return fmt.Errorf("failed to create public key file: %v", err)
	}
	defer publicKeyFile.Close()

	if err := pem.Encode(publicKeyFile, publicKeyPEM); err != nil {
		return fmt.Errorf("failed to write public key: %v", err)
	}

	return nil
}
