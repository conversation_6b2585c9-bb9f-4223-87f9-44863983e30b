// 调试GUI版本与工作版本的差异
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}
	
	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}
	
	return privateKey, nil
}

func analyzeLicense(filename string, description string) {
	fmt.Printf("\n🔍 Analyzing %s (%s)\n", filename, description)
	fmt.Println("=" + fmt.Sprintf("%*s", len(filename)+len(description)+15, "="))
	
	// 1. 加载许可证
	licenseData, err := os.ReadFile(filename)
	if err != nil {
		fmt.Printf("❌ Failed to read %s: %v\n", filename, err)
		return
	}
	
	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		fmt.Printf("❌ Failed to parse %s: %v\n", filename, err)
		return
	}
	
	fmt.Printf("📋 License Info:\n")
	fmt.Printf("  Company: %s\n", license.CompanyName)
	fmt.Printf("  Email: %s\n", license.Email)
	fmt.Printf("  Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  Expiration: %s\n", license.ExpirationDate)
	fmt.Printf("  Encrypted MachineID: %s...\n", license.EncryptedMachineID[:50])
	fmt.Printf("  Signature: %s...\n", license.Signature[:50])
	
	// 2. 加载私钥
	privateKey, err := loadPrivateKey()
	if err != nil {
		fmt.Printf("❌ Failed to load private key: %v\n", err)
		return
	}
	
	// 3. 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		fmt.Printf("❌ Failed to decode encrypted machine ID: %v\n", err)
		return
	}
	
	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		fmt.Printf("❌ Failed to decrypt machine ID: %v\n", err)
		return
	}
	
	fmt.Printf("🔓 Decrypted Machine ID: %s\n", string(decryptedMachineID))
	
	// 4. 重构签名数据
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		fmt.Printf("❌ Failed to parse expiration date: %v\n", err)
		return
	}
	
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		fmt.Printf("❌ Failed to marshal signature data: %v\n", err)
		return
	}
	
	fmt.Printf("📋 Signature JSON: %s\n", string(jsonData))
	fmt.Printf("🔑 Machine ID Hash: %s\n", sigData.MachineIDHash)
	fmt.Printf("📅 Expiration Unix: %d\n", sigData.ExpirationUnix)
	
	// 5. 验证签名
	hash := sha256.Sum256(jsonData)
	
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		fmt.Printf("❌ Failed to decode signature: %v\n", err)
		return
	}
	
	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ Signature verification failed: %v\n", err)
		fmt.Printf("   Hash: %x\n", hash)
		fmt.Printf("   Signature length: %d bytes\n", len(signature))
	} else {
		fmt.Printf("✅ Signature verification successful!\n")
	}
}

func compareMachineIDs() {
	fmt.Println("\n🔍 Comparing Machine IDs")
	fmt.Println("========================")
	
	// 加载机器信息文件
	machineData, err := os.ReadFile("factory_machine_info.json")
	if err != nil {
		fmt.Printf("❌ Failed to read machine info: %v\n", err)
		return
	}
	
	var machineInfo map[string]interface{}
	err = json.Unmarshal(machineData, &machineInfo)
	if err != nil {
		fmt.Printf("❌ Failed to parse machine info: %v\n", err)
		return
	}
	
	machineInfoID := machineInfo["MachineID"].(string)
	fmt.Printf("📂 Machine Info MachineID: %s...\n", machineInfoID[:50])
	
	// 加载GUI生成的license
	guiData, err := os.ReadFile("factory_license.json")
	if err == nil {
		var guiLicense LicenseData
		json.Unmarshal(guiData, &guiLicense)
		fmt.Printf("🖥️  GUI License MachineID:  %s...\n", guiLicense.EncryptedMachineID[:50])
		
		if guiLicense.EncryptedMachineID == machineInfoID {
			fmt.Println("✅ GUI license uses SAME MachineID as machine info")
		} else {
			fmt.Println("❌ GUI license uses DIFFERENT MachineID from machine info")
		}
	}
	
	// 加载工作版本的license
	workingData, err := os.ReadFile("fresh_factory_license.json")
	if err == nil {
		var workingLicense LicenseData
		json.Unmarshal(workingData, &workingLicense)
		fmt.Printf("✅ Working License MachineID: %s...\n", workingLicense.EncryptedMachineID[:50])
		
		if workingLicense.EncryptedMachineID == machineInfoID {
			fmt.Println("✅ Working license uses SAME MachineID as machine info")
		} else {
			fmt.Println("❌ Working license uses DIFFERENT MachineID from machine info")
		}
	}
}

func main() {
	fmt.Println("🚀 Debug: GUI vs Working License Analysis")
	fmt.Println("=========================================")
	
	// 分析GUI生成的license
	if _, err := os.Stat("factory_license.json"); err == nil {
		analyzeLicense("factory_license.json", "GUI Generated - FAILING")
	}
	
	// 分析工作的license
	if _, err := os.Stat("fresh_factory_license.json"); err == nil {
		analyzeLicense("fresh_factory_license.json", "Script Generated - WORKING")
	}
	
	// 比较MachineID
	compareMachineIDs()
	
	fmt.Println("\n📋 Analysis Summary:")
	fmt.Println("- Compare the signature JSONs and machine ID hashes")
	fmt.Println("- Check if the encrypted machine IDs decrypt to the same raw ID")
	fmt.Println("- Identify why GUI version fails validation")
}
