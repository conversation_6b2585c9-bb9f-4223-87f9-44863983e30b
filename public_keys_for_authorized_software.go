package main

// Public keys for V27 license validation with Company ID support
// These keys must be embedded in your authorized software

// Public key for decrypting machine ID from license
const MACHINE_ID_DECRYPTION_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

// Public key for verifying license signature
const SIGNATURE_VERIFICATION_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// Public key for decrypting company ID from encrypted_data_block (NEW in V27)
const COMPANY_ID_DECRYPTION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3Z3byqYgLkfzbNZxJrrV
Ihap1qbuYPzMlBPXGr6h4LTq+gXj/iSpW2n1qDh1r7tvz10xIiQiU35M6Bcgz868
zjGiKAkmbVWdfJcKlW7PPtYTWgywwRZCSW268aY4qF9pmHYibff+D2HI3XAkQFTK
b1PriIwFDAxYIUdZ8MLzmujMzpyymg2mF5ROWk+38zp/F4piSWLYSDTED3S5lQv/
dc9Zrvd5kgQx6V2R4JLf2V/2OfuW6z0L4s9JHSNzAJvA7L4RNFqO4vTAHy6kojy2
Tjwx/xgpHRXQPHF9v4vr6wEibqYA1tDNA4Kr7LchiBCX6/2hZy4Q+sLePh+Wwxgf
LQIDAQAB
-----END PUBLIC KEY-----`

/*
INTEGRATION INSTRUCTIONS FOR AUTHORIZED SOFTWARE:
=================================================

1. COPY THESE THREE CONSTANTS to your authorized software code

2. UPDATE YOUR LICENSE DATA STRUCTURE:
   Add the new field: EncryptedDataBlock string `json:"encrypted_data_block"`

3. UPDATE YOUR SIGNATURE DATA STRUCTURE:
   Add the new field: CompanyIDHash string `json:"c"`

4. ADD COMPANY ID DECRYPTION FUNCTION:
   Use COMPANY_ID_DECRYPTION_PUBLIC_KEY to decrypt encrypted_data_block

5. UPDATE SIGNATURE VERIFICATION:
   Include company ID hash in signature verification process

6. EXAMPLE USAGE:
   
   // Load license
   license, err := LoadLicense("factory_license.json")
   
   // Decrypt company ID (NEW)
   companyIDKey, _ := LoadPublicKeyFromString(COMPANY_ID_DECRYPTION_PUBLIC_KEY)
   companyID, _ := DecryptData(license.EncryptedDataBlock, companyIDKey)
   
   // Verify signature (including company ID hash)
   signatureKey, _ := LoadPublicKeyFromString(SIGNATURE_VERIFICATION_PUBLIC_KEY)
   err = VerifyLicenseSignature(license, machineID, companyID, signatureKey)

7. COMPANY ID FORMAT:
   - 7 digits, no hyphen (e.g., "1234567")
   - Range: 1000000 to 9999999
   - Use for company-specific features

8. SECURITY NOTES:
   - Company ID is never stored in plaintext in license file
   - Field name "encrypted_data_block" obscures its purpose
   - Signature includes company ID hash for integrity
   - All three keys are required for complete validation

For detailed implementation guide, see:
- AUTHORIZED_SOFTWARE_INTEGRATION_GUIDE.md
- license_validator_v27_with_company_id.go (complete example)
*/
