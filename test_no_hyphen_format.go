package main

import (
	"fmt"
)

func main() {
	fmt.Println("🔍 Testing No-Hyphen Company ID Format")
	fmt.Println("=======================================")

	// Test 1: Company ID Formatting (No Hyphen)
	fmt.Println("\n📝 Test 1: Company ID Formatting")
	fmt.Println("---------------------------------")

	testIDs := []int{1000000, 1000001, 1234567, 9999999}
	for _, id := range testIDs {
		formatted := FormatCompanyID(id)
		parsed, err := ParseCompanyID(formatted)
		if err != nil {
			fmt.Printf("❌ Error parsing %s: %v\n", formatted, err)
		} else if parsed != id {
			fmt.Printf("❌ Mismatch: %d -> %s -> %d\n", id, formatted, parsed)
		} else {
			fmt.Printf("✅ %d -> %s -> %d\n", id, formatted, parsed)
		}
	}

	// Test 2: Company ID Validation (No Hyphen)
	fmt.Println("\n✅ Test 2: Company ID Validation")
	fmt.Println("---------------------------------")

	// Load registry for validation
	registry, err := LoadCompanyRegistry()
	if err != nil {
		fmt.Printf("❌ Failed to load registry: %v\n", err)
		return
	}

	validIDs := []string{"1000000", "1234567", "9999999"}
	invalidIDs := []string{"123456", "12345678", "123-4567", "abcdefg"}

	for _, id := range validIDs {
		if err := ValidateCompanyID(id, registry); err != nil {
			fmt.Printf("❌ Valid ID rejected: %s - %v\n", id, err)
		} else {
			fmt.Printf("✅ Valid ID accepted: %s\n", id)
		}
	}

	for _, id := range invalidIDs {
		if err := ValidateCompanyID(id, registry); err == nil {
			fmt.Printf("❌ Invalid ID accepted: %s\n", id)
		} else {
			fmt.Printf("✅ Invalid ID rejected: %s - %v\n", id, err)
		}
	}

	// Test 3: Hash Comparison (No Hyphen vs With Hyphen)
	fmt.Println("\n🔍 Test 3: Hash Comparison")
	fmt.Println("--------------------------")

	noHyphen := hashString("1234567")
	withHyphen := hashString("123-4567")

	fmt.Printf("No hyphen:   %s\n", noHyphen)
	fmt.Printf("With hyphen: %s\n", withHyphen)

	if noHyphen == withHyphen {
		fmt.Println("❌ ERROR: Hashes are the same! This should not happen.")
	} else {
		fmt.Println("✅ CORRECT: Hashes are different. No hyphen format is used in signature.")
	}

	// Test 4: Company Registry Format
	fmt.Println("\n📋 Test 4: Company Registry Format")
	fmt.Println("----------------------------------")

	// Test auto-generation
	testCompany := "Test Company No Hyphen"
	testEmail := "<EMAIL>"
	testPhone := "18888888888"

	companyID, err := GetOrCreateCompanyID(testCompany, testEmail, testPhone, registry)
	if err != nil {
		fmt.Printf("❌ Failed to create company ID: %v\n", err)
	} else {
		fmt.Printf("✅ Generated company ID: %s\n", companyID)
		fmt.Printf("   Format check: %d digits, no hyphen\n", len(companyID))
	}

	// Save registry
	if err := SaveCompanyRegistry(registry); err != nil {
		fmt.Printf("❌ Failed to save registry: %v\n", err)
	} else {
		fmt.Printf("✅ Registry saved successfully\n")
	}

	fmt.Println("\n🎯 Summary")
	fmt.Println("----------")
	fmt.Println("✅ Company ID format: 7 digits, no hyphen (e.g., 1234567)")
	fmt.Println("✅ Signature uses no-hyphen format")
	fmt.Println("✅ License file field name obscured: 'encrypted_data_block'")
	fmt.Println("✅ No backward compatibility with hyphen format")
}
