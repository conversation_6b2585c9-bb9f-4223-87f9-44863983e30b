package main

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
)

// Simple hash function for testing
func testHashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func main() {
	fmt.Println("🔍 Company ID Hash Test")
	fmt.Println("=======================")

	// Test with and without hyphen
	withHyphen := testHashString("123-4567")
	withoutHyphen := testHashString("1234567")

	fmt.Printf("Company ID with hyphen:    '123-4567' → %s\n", withHyphen)
	fmt.Printf("Company ID without hyphen: '1234567'  → %s\n", withoutHyphen)

	if withHyphen == withoutHyphen {
		fmt.Println("\n❌ ERROR: Hashes are identical!")
		fmt.Println("   This means the hyphen is NOT affecting the signature.")
	} else {
		fmt.Println("\n✅ SUCCESS: Hashes are different!")
		fmt.Println("   This confirms the hyphen IS included in the signature.")
	}

	fmt.Println("\n📋 Conclusion:")
	fmt.Println("   The company ID format '123-4567' (with hyphen) is used in signature generation.")
	fmt.Println("   License validators must use the exact same format for verification.")
}
