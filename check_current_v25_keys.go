// check_current_v25_keys.go
// 检查当前V25软件实际使用的密钥

package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"math/big"
	"os"
)

func main() {
	fmt.Println("🔍 Checking Current V25 Software Key Usage")
	fmt.Println("==========================================")

	// 检查V25软件实际使用的密钥文件
	fmt.Println("📝 Checking keys used by V25 software...")

	// 1. 机器解密密钥 (从配置文件或默认路径)
	machineKeyPath := "machine_decryption_private_key_to_decryp_factory_machineinfo.pem"
	fmt.Printf("🔑 Machine decryption key: %s\n", machineKeyPath)
	
	machinePrivKey, err := loadPrivateKey(machineKeyPath)
	if err != nil {
		fmt.Printf("❌ Failed to load machine key: %v\n", err)
		return
	}

	// 2. 签名生成密钥 (硬编码在main.go中)
	signatureKeyPath := "license_signing_private_key.pem"
	fmt.Printf("✍️  Signature generation key: %s\n", signatureKeyPath)
	
	signaturePrivKey, err := loadPrivateKey(signatureKeyPath)
	if err != nil {
		fmt.Printf("❌ Failed to load signature key: %v\n", err)
		return
	}

	fmt.Println("✅ Both keys loaded successfully")

	// 3. 检查密钥是否不同
	fmt.Println("\n🔍 Key Separation Analysis:")
	fmt.Printf("Machine Key Fingerprint:   %s\n", getKeyFingerprint(machinePrivKey.N))
	fmt.Printf("Signature Key Fingerprint: %s\n", getKeyFingerprint(signaturePrivKey.N))

	if machinePrivKey.N.Cmp(signaturePrivKey.N) == 0 {
		fmt.Println("❌ PROBLEM: V25 software is using THE SAME key for both purposes!")
		fmt.Println("   当前软件使用的是同一个密钥，不是真正分离的")
	} else {
		fmt.Println("✅ EXCELLENT: V25 software is using DIFFERENT keys!")
		fmt.Println("   当前软件使用的是真正分离的两个不同密钥")
	}

	// 4. 检查与我们生成的正确密钥的关系
	fmt.Println("\n📋 Comparison with Generated Correct Keys:")
	
	// 检查是否与正确的机器密钥匹配
	if _, err := os.Stat("machine_id_decryption_private_key_CORRECT.pem"); err == nil {
		correctMachineKey, err := loadPrivateKey("machine_id_decryption_private_key_CORRECT.pem")
		if err == nil {
			if machinePrivKey.N.Cmp(correctMachineKey.N) == 0 {
				fmt.Println("✅ Machine key matches our generated CORRECT machine key")
			} else {
				fmt.Println("⚠️  Machine key differs from our generated CORRECT machine key")
			}
		}
	}

	// 检查是否与正确的签名密钥匹配
	if _, err := os.Stat("signature_generation_private_key_CORRECT.pem"); err == nil {
		correctSignatureKey, err := loadPrivateKey("signature_generation_private_key_CORRECT.pem")
		if err == nil {
			if signaturePrivKey.N.Cmp(correctSignatureKey.N) == 0 {
				fmt.Println("✅ Signature key matches our generated CORRECT signature key")
			} else {
				fmt.Println("⚠️  Signature key differs from our generated CORRECT signature key")
			}
		}
	}

	// 5. 最终结论
	fmt.Println("\n🎯 Final Assessment:")
	fmt.Println("====================")
	
	if machinePrivKey.N.Cmp(signaturePrivKey.N) != 0 {
		fmt.Println("🎉 YES: Current V25 software uses truly separated key pairs!")
		fmt.Println("   ✅ Machine decryption key: Different")
		fmt.Println("   ✅ Signature generation key: Different")
		fmt.Println("   ✅ Key separation: Achieved")
		fmt.Println()
		fmt.Println("   当前软件确实使用了真正分离的两对独立密钥!")
	} else {
		fmt.Println("❌ NO: Current V25 software is still using the same key!")
		fmt.Println("   需要更新软件以使用分离的密钥")
	}

	// 6. 显示密钥用途
	fmt.Println("\n📋 Current Key Usage in V25:")
	fmt.Println("============================")
	fmt.Printf("🔐 Machine ID Decryption: %s (%s)\n", machineKeyPath, getKeyFingerprint(machinePrivKey.N))
	fmt.Printf("✍️  Digital Signature:     %s (%s)\n", signatureKeyPath, getKeyFingerprint(signaturePrivKey.N))
}

func loadPrivateKey(filename string) (*rsa.PrivateKey, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(data)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}

func getKeyFingerprint(n *big.Int) string {
	bytes := n.Bytes()
	if len(bytes) >= 4 {
		return fmt.Sprintf("%x", bytes[:4])
	}
	return fmt.Sprintf("%x", bytes)
}
