# License Type Feature Guide
# 许可证类型功能指南

## 🎯 Overview / 概述

The License Generator now supports three different license types through an intuitive radio button interface. Users can select the appropriate license type based on their specific needs.

许可证生成器现在通过直观的单选按钮界面支持三种不同的许可证类型。用户可以根据具体需求选择合适的许可证类型。

## 🏷️ License Types / 许可证类型

### 1. **Lease** (Default / 默认)
- **Purpose**: Temporary usage rights with defined expiration
- **Use Case**: Standard commercial licensing, subscription-based software
- **Characteristics**: 
  - ✅ Time-limited access
  - ✅ Renewable upon expiration
  - ✅ Most common license type

### 1. **Lease** (默认)
- **用途**: 具有明确过期时间的临时使用权
- **使用场景**: 标准商业许可、基于订阅的软件
- **特点**:
  - ✅ 限时访问
  - ✅ 到期后可续期
  - ✅ 最常见的许可证类型

### 2. **Demo**
- **Purpose**: Trial or demonstration version
- **Use Case**: Software evaluation, proof of concept, testing
- **Characteristics**:
  - ✅ Limited functionality or time
  - ✅ Evaluation purposes only
  - ✅ Often shorter duration

### 2. **Demo**
- **用途**: 试用或演示版本
- **使用场景**: 软件评估、概念验证、测试
- **特点**:
  - ✅ 功能或时间受限
  - ✅ 仅用于评估目的
  - ✅ 通常持续时间较短

### 3. **Perpetual**
- **Purpose**: Permanent usage rights
- **Use Case**: One-time purchase, lifetime licenses
- **Characteristics**:
  - ✅ No expiration date enforcement
  - ✅ Permanent access to software
  - ✅ Highest value license type

### 3. **Perpetual**
- **用途**: 永久使用权
- **使用场景**: 一次性购买、终身许可
- **特点**:
  - ✅ 无过期日期强制执行
  - ✅ 软件永久访问权
  - ✅ 最高价值的许可证类型

## 🖥️ User Interface / 用户界面

### License Type Selection Panel / 许可证类型选择面板

```
🏷️ License Type
┌─────────────────────────────────────────┐
│ Select license type:                    │
│                                         │
│ ○ lease    ○ demo    ○ perpetual        │
│   (●)        ( )        ( )             │
│                                         │
└─────────────────────────────────────────┘
```

#### Interface Features / 界面特性
- **Radio Button Group**: Horizontal layout for easy selection
- **Default Selection**: "lease" is pre-selected
- **Visual Feedback**: Clear indication of selected option
- **Responsive Design**: Adapts to different screen sizes

#### 界面特性
- **单选按钮组**: 水平布局便于选择
- **默认选择**: 预选"lease"
- **视觉反馈**: 清楚显示选中选项
- **响应式设计**: 适应不同屏幕尺寸

## 🔧 Technical Implementation / 技术实现

### Data Structure Updates / 数据结构更新

#### LicenseData Structure / 许可证数据结构
```go
type LicenseData struct {
    CompanyName        string `json:"company_name"`
    Email              string `json:"email"`
    Phone              string `json:"phone"`
    AuthorizedSoftware string `json:"authorized_software"`
    AuthorizedVersion  string `json:"authorized_version"`
    LicenseType        string `json:"license_type"`        // NEW FIELD
    ExpirationDate     string `json:"expiration_date"`
    IssuedDate         string `json:"issued_date"`
    EncryptedMachineID string `json:"encrypted_machine_id"`
    Signature          string `json:"signature"`
}
```

#### SignatureData Structure / 签名数据结构
```go
type SignatureData struct {
    CompanyName    string `json:"c"`
    Email          string `json:"e"`
    Software       string `json:"s"`
    Version        string `json:"v"`
    LicenseType    string `json:"t"`  // NEW FIELD
    ExpirationUnix int64  `json:"x"`
    MachineIDHash  string `json:"m"`
}
```

### UI Component Implementation / UI组件实现

#### Radio Button Creation / 单选按钮创建
```go
// License Type selection with radio buttons
licenseTypeOptions := []string{"lease", "demo", "perpetual"}
licenseTypeRadio := widget.NewRadioGroup(licenseTypeOptions, nil)
licenseTypeRadio.SetSelected("lease") // Default to lease
licenseTypeRadio.Horizontal = true    // Display horizontally
```

#### License Generation / 许可证生成
```go
license, err := generator.GenerateLicense(
    companyName,
    email,
    phone,
    softwareName,
    version,
    licenseTypeRadio.Selected,  // Pass selected license type
    expirationDate,
)
```

## 📋 Generated License Format / 生成的许可证格式

### JSON Output Example / JSON输出示例

```json
{
  "company_name": "Example Company",
  "email": "<EMAIL>",
  "phone": "************",
  "authorized_software": "LS-DYNA Model License Generate Factory",
  "authorized_version": "2.3.0",
  "license_type": "lease",
  "expiration_date": "2026-01-10",
  "issued_date": "2025-07-11",
  "encrypted_machine_id": "Base64EncodedMachineID...",
  "signature": "Base64EncodedSignature..."
}
```

### Signature Data Example / 签名数据示例

```json
{
  "c": "Example Company",
  "e": "<EMAIL>",
  "s": "LS-DYNA Model License Generate Factory",
  "v": "2.3.0",
  "t": "lease",
  "x": **********,
  "m": "HashedMachineID"
}
```

## 🎯 Usage Workflow / 使用工作流

### Step-by-Step Process / 分步流程

#### 1. **Configure Files / 配置文件**
- Load machine info JSON file
- Load private key PEM file

#### 2. **Fill License Details / 填写许可证详情**
- Company information
- Software details

#### 3. **Select License Type / 选择许可证类型** 🆕
- Choose from: lease, demo, perpetual
- Default: lease

#### 4. **Set Expiration Date / 设置过期日期**
- Use preset options or custom date
- Note: Perpetual licenses still have expiration for signature purposes

#### 5. **Generate License / 生成许可证**
- Click "Generate License" button
- License type included in generated file

### 分步流程

#### 1. **配置文件**
- 加载机器信息JSON文件
- 加载私钥PEM文件

#### 2. **填写许可证详情**
- 公司信息
- 软件详情

#### 3. **选择许可证类型** 🆕
- 从lease、demo、perpetual中选择
- 默认：lease

#### 4. **设置过期日期**
- 使用预设选项或自定义日期
- 注意：永久许可证仍有过期日期用于签名目的

#### 5. **生成许可证**
- 点击"生成许可证"按钮
- 许可证类型包含在生成的文件中

## ✅ Success Dialog Enhancement / 成功对话框增强

### Enhanced Information Display / 增强信息显示

```
License generated and saved successfully!

📁 File: factory_license.json
🗂️ Folder opened automatically

Company: Example Company
Software: LS-DYNA Model License Generate Factory v2.3.0
🏷️ Type: lease                    ← NEW FIELD
Expires: 2026-01-10
```

## 🔍 Validation and Security / 验证和安全性

### License Type Validation / 许可证类型验证

#### Client-Side Validation / 客户端验证
- **Required Selection**: User must select a license type
- **Default Value**: "lease" is automatically selected
- **Input Validation**: Only predefined types accepted

#### Server-Side Security / 服务器端安全
- **Signature Inclusion**: License type included in digital signature
- **Tamper Detection**: Any modification to license type invalidates signature
- **Type Verification**: Authorized software can verify license type

### 许可证类型验证

#### 客户端验证
- **必需选择**: 用户必须选择许可证类型
- **默认值**: 自动选择"lease"
- **输入验证**: 仅接受预定义类型

#### 服务器端安全
- **签名包含**: 许可证类型包含在数字签名中
- **篡改检测**: 对许可证类型的任何修改都会使签名无效
- **类型验证**: 被授权软件可以验证许可证类型

## 🚀 Version Information / 版本信息

### V22 Features / V22版本特性

- ✅ **License Type Selection** / 许可证类型选择
- ✅ **Radio Button Interface** / 单选按钮界面
- ✅ **Default to Lease** / 默认为lease
- ✅ **Signature Integration** / 签名集成
- ✅ **Enhanced Success Dialog** / 增强成功对话框
- ✅ **JSON Format Update** / JSON格式更新

### Backward Compatibility / 向后兼容性

- **Existing Licenses**: Old licenses without license_type field remain valid
- **Signature Verification**: New signature format includes license type
- **Migration Path**: Existing systems can be updated to recognize license types

### 向后兼容性

- **现有许可证**: 没有license_type字段的旧许可证仍然有效
- **签名验证**: 新签名格式包含许可证类型
- **迁移路径**: 现有系统可以更新以识别许可证类型

## 📋 Best Practices / 最佳实践

### License Type Selection Guidelines / 许可证类型选择指南

#### When to Use "lease" / 何时使用"lease"
- Standard commercial software licensing
- Subscription-based services
- Time-limited access requirements

#### When to Use "demo" / 何时使用"demo"
- Software evaluation periods
- Proof of concept projects
- Limited functionality testing

#### When to Use "perpetual" / 何时使用"perpetual"
- One-time purchase software
- Lifetime access grants
- Permanent installation rights

### 许可证类型选择指南

#### 何时使用"lease"
- 标准商业软件许可
- 基于订阅的服务
- 限时访问需求

#### 何时使用"demo"
- 软件评估期
- 概念验证项目
- 有限功能测试

#### 何时使用"perpetual"
- 一次性购买软件
- 终身访问授权
- 永久安装权限

---

**Note**: The License Type feature enhances the flexibility and clarity of license management while maintaining full security and compatibility.

**注意**: 许可证类型功能增强了许可证管理的灵活性和清晰度，同时保持完全的安全性和兼容性。
