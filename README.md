# License Generator

A Go application built with Fyne framework for generating software licenses with RSA2048 encryption and digital signatures.

## ✅ Status: FULLY FUNCTIONAL

The application has been successfully built and tested. Both console and GUI versions are working perfectly.

## 🚀 Quick Start

1. **Double-click `license-generator-gui.exe`** to start the GUI application
2. Select your machine info JSON file and private key PEM file
3. Fill in license details and choose expiration date
4. Generate and save your license!

## Features

- **File Selection**: Easy selection of machine info JSON files and RSA private key PEM files
- **Machine ID Decryption**: Automatically decrypts encrypted machine IDs using RSA2048 private keys
- **License Generation**: Creates comprehensive licenses with company details, software info, and expiration dates
- **Flexible Expiration**: Quick presets (1 month+2 days, 3 months+2 days, 1 year+2 days) or custom date input
- **Digital Signatures**: Compact RSA signatures that fit within RSA2048 size limits
- **Export Functionality**: Save generated licenses to JSON files
- **User-Friendly GUI**: Clean English interface built with Fyne
- **No Console Window**: Pure GUI experience when using the GUI version

## Requirements

- Go 1.21.1 or later
- Windows, macOS, or Linux
- RSA2048 private key in PEM format
- Machine info file in JSON format

## Available Executables

- **`license-generator-gui.exe`** - Pure GUI version (recommended for end users)
- **`license-generator.exe`** - Console version with debug output

## Installation & Building

1. Clone or download the project
2. Set up Go proxy (for Chinese users):
   ```bash
   go env -w GOPROXY=https://mirrors.aliyun.com/goproxy/,direct
   ```
3. Build the applications:
   ```bash
   # GUI version (no console window)
   go build -ldflags "-H windowsgui" -o license-generator-gui.exe .

   # Console version (with debug output)
   go build -o license-generator.exe .
   ```

## Usage

### Running the Application

**For End Users (Recommended):**
- Double-click `license-generator-gui.exe`

**For Developers/Debugging:**
```bash
./license-generator.exe
```

### Step-by-Step Guide

1. **Select Machine Info File**: Click "Browse" next to "Machine Info File" and select your JSON file
2. **Select Private Key File**: Click "Browse" next to "Private Key File" and select your PEM file
3. **Fill License Details**:
   - Company Name (auto-populated from machine info)
   - Email Address (auto-populated from machine info)
   - Phone Number (auto-populated from machine info)
   - Authorized Software
   - Version
4. **Set Expiration Date**:
   - Choose from presets: "1 Month + 2 Days", "3 Months + 2 Days", "1 Year + 2 Days"
   - Or select "Custom Date" and enter date in YYYY-MM-DD format
5. **Generate License**: Click "Generate License" button
6. **Save License**: Click "Save License" to export to JSON file

### Machine Info File Format

```json
{
  "CompanyName": "Company Name",
  "Email": "<EMAIL>",
  "Phone": "************",
  "MachineID": "base64-encoded-encrypted-machine-id",
  "GeneratedBy": "Factory Software",
  "GeneratedDate": "2025-07-08 22:50:16",
  "Notes": "Additional notes"
}
```

### Generated License Format

```json
{
  "company_name": "Company Name",
  "email": "<EMAIL>",
  "phone": "************",
  "authorized_software": "Software Name",
  "version": "1.0.0",
  "expiration_date": "2025-08-10T01:18:55.7956604+08:00",
  "machine_id": "decrypted-machine-id",
  "signature": "base64-encoded-rsa-signature",
  "generated_date": "2025-07-09T01:18:55.796207+08:00"
}
```

## Testing

Run the test suite to validate functionality:

```bash
go run test_license.go models.go crypto.go license.go
```

## Security Features

- **RSA2048 Encryption**: Uses industry-standard RSA2048 for machine ID decryption
- **Digital Signatures**: Each license includes a cryptographic signature for verification
- **Compact Signature Format**: Optimized signature data structure to fit RSA2048 size limits
- **Hash-based Verification**: Machine ID is hashed for signature compactness while maintaining security

## Architecture

- `main.go`: Application entry point
- `models.go`: Data structures and models
- `crypto.go`: RSA encryption/decryption and signature functions
- `license.go`: Core license generation logic
- `ui.go`: Fyne-based user interface
- `test_license.go`: Test suite for validation

## License

This software is designed for license generation and management purposes.
