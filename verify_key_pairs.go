// verify_key_pairs.go
// 验证密钥对关系

package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔍 Verifying Key Pair Relationships")
	fmt.Println("===================================")

	// 1. 检查签名验证公钥
	fmt.Println("📝 1. Loading signature verification public key...")
	sigPubKey, err := loadPublicKey("signature_verification_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load signature public key: %v\n", err)
		return
	}
	fmt.Printf("✅ Signature public key loaded (size: %d bits)\n", sigPubKey.Size()*8)

	// 2. 检查机器ID解密私钥
	fmt.Println("\n📝 2. Loading machine ID decryption private key...")
	machinePrivKey, err := loadPrivateKey("machine_id_decryption_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load machine private key: %v\n", err)
		return
	}
	fmt.Printf("✅ Machine private key loaded (size: %d bits)\n", machinePrivKey.Size()*8)

	// 3. 提取机器私钥对应的公钥
	fmt.Println("\n📝 3. Extracting public key from machine private key...")
	machinePublicKey := &machinePrivKey.PublicKey

	// 4. 比较两个公钥
	fmt.Println("\n🔍 4. Comparing public keys...")
	
	// 比较公钥的N值（模数）
	if sigPubKey.N.Cmp(machinePublicKey.N) == 0 {
		fmt.Println("❌ PROBLEM FOUND: The keys are from the SAME key pair!")
		fmt.Println("   签名验证公钥 ↔ 机器ID解密私钥: 是同一密钥对")
	} else {
		fmt.Println("✅ GOOD: The keys are from DIFFERENT key pairs!")
		fmt.Println("   签名验证公钥 ↔ 机器ID解密私钥: 是不同密钥对")
	}

	// 5. 显示密钥指纹
	fmt.Println("\n📋 Key Fingerprints:")
	fmt.Println("====================")
	
	sigPubKeyBytes := x509.MarshalPKCS1PublicKey(sigPubKey)
	machPubKeyBytes := x509.MarshalPKCS1PublicKey(machinePublicKey)
	
	fmt.Printf("🔍 Signature Public Key: %x...\n", sigPubKeyBytes[:16])
	fmt.Printf("🔑 Machine Public Key:   %x...\n", machPubKeyBytes[:16])

	// 6. 测试加密解密验证
	fmt.Println("\n🧪 Testing encryption/decryption compatibility...")
	testMessage := []byte("Test message for key pair verification")
	
	// 用签名公钥加密
	encryptedWithSigKey, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, sigPubKey, testMessage, nil)
	if err != nil {
		fmt.Printf("❌ Failed to encrypt with signature public key: %v\n", err)
		return
	}
	
	// 尝试用机器私钥解密
	_, err = rsa.DecryptOAEP(sha256.New(), rand.Reader, machinePrivKey, encryptedWithSigKey, nil)
	if err == nil {
		fmt.Println("❌ CONFIRMED: Keys are from the same pair (decryption successful)")
		fmt.Println("   确认: 密钥来自同一密钥对（解密成功）")
	} else {
		fmt.Println("✅ CONFIRMED: Keys are from different pairs (decryption failed)")
		fmt.Println("   确认: 密钥来自不同密钥对（解密失败）")
	}

	// 7. 检查当前V25生成器实际使用的密钥
	fmt.Println("\n🔧 Checking V25 generator actual key usage...")
	
	// 检查license_signing_private_key.pem
	fmt.Println("📝 Loading license signing private key...")
	licenseSigningKey, err := loadPrivateKey("license_signing_private_key.pem")
	if err != nil {
		fmt.Printf("⚠️ License signing key not found: %v\n", err)
	} else {
		licenseSigningPubKey := &licenseSigningKey.PublicKey
		licenseSigningPubKeyBytes := x509.MarshalPKCS1PublicKey(licenseSigningPubKey)
		fmt.Printf("✅ License Signing Public Key: %x...\n", licenseSigningPubKeyBytes[:16])
		
		// 比较license signing key和signature verification key
		if sigPubKey.N.Cmp(licenseSigningPubKey.N) == 0 {
			fmt.Println("✅ GOOD: Signature verification key matches license signing key")
		} else {
			fmt.Println("❌ PROBLEM: Signature verification key does NOT match license signing key")
		}
	}

	fmt.Println("\n🎯 Summary:")
	fmt.Println("===========")
	fmt.Println("The authorized software is correct!")
	fmt.Println("被授权软件的反馈是正确的！")
	fmt.Println()
	fmt.Println("Issue: The signature verification public key we provided")
	fmt.Println("       matches the machine ID decryption private key.")
	fmt.Println("问题: 我们提供的签名验证公钥与机器ID解密私钥是同一密钥对")
}

func loadPublicKey(filename string) (*rsa.PublicKey, error) {
	keyData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return publicKey, nil
}

func loadPrivateKey(filename string) (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}
