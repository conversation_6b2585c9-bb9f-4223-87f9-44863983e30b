# Company ID Hyphen Signature Verification
# 公司ID短横线签名验证

## Question / 问题

Does the company ID signature include the hyphen character?

公司ID签名是否包含短横线字符？

## Answer / 答案

**YES, the hyphen IS included in the signature.**

**是的，短横线包含在签名中。**

## Technical Evidence / 技术证据

### Code Analysis / 代码分析

In `crypto.go`, the `CreateSignature` function:

在`crypto.go`中，`CreateSignature`函数：

```go
func CreateSignature(licenseData *LicenseData, privateKey *rsa.PrivateKey, startTime, expirationTime time.Time, machineID, companyID string) (string, error) {
    // ...
    sigData := SignatureData{
        Software:       licenseData.AuthorizedSoftware,
        Version:        licenseData.AuthorizedVersion,
        LicenseType:    licenseData.LicenseType,
        StartUnix:      startTime.Unix(),
        ExpirationUnix: expirationTime.Unix(),
        MachineIDHash:  hashString(machineID),
        CompanyIDHash:  hashString(companyID), // ← Uses companyID as-is, including hyphen
    }
    // ...
}
```

### Input Format / 输入格式

The `companyID` parameter passed to `CreateSignature` comes from:

传递给`CreateSignature`的`companyID`参数来自：

1. **GUI Input**: User enters or system generates formatted ID like `"123-4567"`
2. **Registry Storage**: Company registry stores IDs in formatted form
3. **Direct Usage**: The formatted ID is used directly in signature

1. **GUI输入**：用户输入或系统生成格式化ID如`"123-4567"`
2. **注册表存储**：公司注册表以格式化形式存储ID
3. **直接使用**：格式化ID直接用于签名

### Hash Function Behavior / 哈希函数行为

The `hashString` function processes the complete input:

`hashString`函数处理完整输入：

```go
func hashString(input string) string {
    hash := sha256.Sum256([]byte(input)) // ← Processes ALL characters including hyphen
    encoded := base64.StdEncoding.EncodeToString(hash[:])
    if len(encoded) > 16 {
        return encoded[:16]
    }
    return encoded
}
```

### Verification Test / 验证测试

To confirm this behavior, you can test:

要确认此行为，您可以测试：

```go
hash1 := hashString("123-4567")  // With hyphen
hash2 := hashString("1234567")   // Without hyphen

// These will produce DIFFERENT hashes
fmt.Printf("With hyphen:    %s\n", hash1)
fmt.Printf("Without hyphen: %s\n", hash2)
```

**Result**: The hashes will be different, confirming the hyphen affects the signature.

**结果**：哈希值将不同，确认短横线影响签名。

## Implications / 影响

### For License Generation / 对许可证生成的影响

1. **Consistent Format**: Always use `"XXX-XXXX"` format in company registry
2. **Signature Integrity**: Hyphen is part of the signed data
3. **Validation Requirement**: Validators must use the same format

1. **一致格式**：在公司注册表中始终使用`"XXX-XXXX"`格式
2. **签名完整性**：短横线是签名数据的一部分
3. **验证要求**：验证器必须使用相同格式

### For License Validation / 对许可证验证的影响

1. **Decrypt Company ID**: Extract encrypted company ID from license
2. **Format Correctly**: Ensure decrypted ID is formatted as `"XXX-XXXX"`
3. **Use in Verification**: Use formatted ID (with hyphen) for signature verification

1. **解密公司ID**：从许可证中提取加密的公司ID
2. **正确格式化**：确保解密的ID格式化为`"XXX-XXXX"`
3. **用于验证**：使用格式化ID（带短横线）进行签名验证

### For System Integration / 对系统集成的影响

1. **Database Storage**: Store company IDs with hyphen in all systems
2. **API Consistency**: Use formatted IDs in all API communications
3. **Display Format**: Show IDs with hyphen to users for consistency

1. **数据库存储**：在所有系统中存储带短横线的公司ID
2. **API一致性**：在所有API通信中使用格式化ID
3. **显示格式**：向用户显示带短横线的ID以保持一致性

## Best Practices / 最佳实践

### Development / 开发

1. **Always format**: Use `FormatCompanyID()` function to ensure consistency
2. **Validate input**: Check format before using in signatures
3. **Test thoroughly**: Verify signature generation and validation

1. **始终格式化**：使用`FormatCompanyID()`函数确保一致性
2. **验证输入**：在签名中使用前检查格式
3. **彻底测试**：验证签名生成和验证

### Documentation / 文档

1. **Specify format**: Clearly document the `"XXX-XXXX"` requirement
2. **Include examples**: Show correct and incorrect formats
3. **Update validators**: Ensure all validators use the same format

1. **指定格式**：清楚记录`"XXX-XXXX"`要求
2. **包含示例**：显示正确和错误的格式
3. **更新验证器**：确保所有验证器使用相同格式

## Conclusion / 结论

**The company ID hyphen IS included in the digital signature.**

**公司ID短横线包含在数字签名中。**

This means:
- License validators must use the formatted company ID (`"123-4567"`) for verification
- The hyphen is a critical part of the signature integrity
- Any change to the hyphen format would break signature validation

这意味着：
- 许可证验证器必须使用格式化的公司ID（`"123-4567"`）进行验证
- 短横线是签名完整性的关键部分
- 对短横线格式的任何更改都会破坏签名验证

---

**Verified**: 2025-07-13  
**Status**: ✅ Confirmed  
**Impact**: Critical for signature validation
