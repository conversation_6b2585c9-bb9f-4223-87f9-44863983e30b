// verify_four_keys_separation.go
// 验证四个密钥文件是否真的是两对不同的密钥

package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔍 Verifying Four Key Files Separation")
	fmt.Println("======================================")

	// 1. 加载机器ID解密密钥对
	fmt.Println("📝 1. Loading Machine ID Decryption Key Pair...")

	machinePrivKey, err := loadPrivateKeyFromFile("machine_id_decryption_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load machine private key: %v\n", err)
		return
	}

	machinePubKey, err := loadPublicKeyFromFile("machine_id_decryption_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load machine public key: %v\n", err)
		return
	}

	// 2. 加载签名密钥对
	fmt.Println("📝 2. Loading Signature Key Pair...")

	signaturePrivKey, err := loadPrivateKeyFromFile("signature_generation_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load signature private key: %v\n", err)
		return
	}

	signaturePubKey, err := loadPublicKeyFromFile("signature_verification_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load signature public key: %v\n", err)
		return
	}

	fmt.Println("✅ All four key files loaded successfully")

	// 3. 验证机器ID密钥对是否匹配
	fmt.Println("\n🔍 3. Verifying Machine ID Key Pair...")
	machinePrivPubKey := &machinePrivKey.PublicKey

	if machinePubKey.N.Cmp(machinePrivPubKey.N) == 0 && machinePubKey.E == machinePrivPubKey.E {
		fmt.Println("✅ Machine ID key pair matches (private ↔ public)")
	} else {
		fmt.Println("❌ Machine ID key pair does NOT match!")
		return
	}

	// 4. 验证签名密钥对是否匹配
	fmt.Println("\n🔍 4. Verifying Signature Key Pair...")
	signaturePrivPubKey := &signaturePrivKey.PublicKey

	if signaturePubKey.N.Cmp(signaturePrivPubKey.N) == 0 && signaturePubKey.E == signaturePrivPubKey.E {
		fmt.Println("✅ Signature key pair matches (private ↔ public)")
	} else {
		fmt.Println("❌ Signature key pair does NOT match!")
		return
	}

	// 5. 验证两对密钥是否不同
	fmt.Println("\n🔍 5. Verifying Key Pairs Are Different...")

	if machinePrivKey.N.Cmp(signaturePrivKey.N) == 0 {
		fmt.Println("❌ PROBLEM: Machine and Signature private keys are THE SAME!")
		fmt.Println("   机器解密私钥 = 签名生成私钥 (同一密钥)")
	} else {
		fmt.Println("✅ GOOD: Machine and Signature private keys are DIFFERENT")
		fmt.Println("   机器解密私钥 ≠ 签名生成私钥 (不同密钥)")
	}

	if machinePubKey.N.Cmp(signaturePubKey.N) == 0 {
		fmt.Println("❌ PROBLEM: Machine and Signature public keys are THE SAME!")
		fmt.Println("   机器解密公钥 = 签名验证公钥 (同一密钥)")
	} else {
		fmt.Println("✅ GOOD: Machine and Signature public keys are DIFFERENT")
		fmt.Println("   机器解密公钥 ≠ 签名验证公钥 (不同密钥)")
	}

	// 6. 显示密钥指纹
	fmt.Println("\n📋 Key Fingerprints:")
	fmt.Println("====================")

	machinePrivBytes := x509.MarshalPKCS1PrivateKey(machinePrivKey)
	machinePubBytes := x509.MarshalPKCS1PublicKey(machinePubKey)
	signaturePrivBytes := x509.MarshalPKCS1PrivateKey(signaturePrivKey)
	signaturePubBytes := x509.MarshalPKCS1PublicKey(signaturePubKey)

	fmt.Printf("🔑 Machine Private Key:    %x...\n", machinePrivBytes[:16])
	fmt.Printf("🔓 Machine Public Key:     %x...\n", machinePubBytes[:16])
	fmt.Printf("✍️  Signature Private Key:  %x...\n", signaturePrivBytes[:16])
	fmt.Printf("🔍 Signature Public Key:   %x...\n", signaturePubBytes[:16])

	// 7. 交叉验证 (确保不是交叉配对)
	fmt.Println("\n🧪 Cross-Validation Test:")
	fmt.Println("=========================")

	// 检查机器私钥是否与签名公钥配对
	if machinePrivPubKey.N.Cmp(signaturePubKey.N) == 0 {
		fmt.Println("❌ CRITICAL: Machine private key matches signature public key!")
		fmt.Println("   机器解密私钥 ↔ 签名验证公钥 是同一密钥对")
		fmt.Println("   这就是被授权软件反馈的问题!")
	} else {
		fmt.Println("✅ GOOD: Machine private key does NOT match signature public key")
		fmt.Println("   机器解密私钥 ↔ 签名验证公钥 是不同密钥对")
	}

	// 检查签名私钥是否与机器公钥配对
	if signaturePrivPubKey.N.Cmp(machinePubKey.N) == 0 {
		fmt.Println("❌ CRITICAL: Signature private key matches machine public key!")
		fmt.Println("   签名生成私钥 ↔ 机器解密公钥 是同一密钥对")
	} else {
		fmt.Println("✅ GOOD: Signature private key does NOT match machine public key")
		fmt.Println("   签名生成私钥 ↔ 机器解密公钥 是不同密钥对")
	}

	// 8. 最终结论
	fmt.Println("\n🎯 Final Analysis:")
	fmt.Println("==================")

	machineKeysSame := machinePrivKey.N.Cmp(signaturePrivKey.N) == 0
	crossMatch := machinePrivPubKey.N.Cmp(signaturePubKey.N) == 0

	if machineKeysSame || crossMatch {
		fmt.Println("❌ PROBLEM CONFIRMED: The four keys are NOT two separate key pairs!")
		fmt.Println("   确认问题: 四个密钥文件不是两对独立的密钥对!")
		fmt.Println()
		fmt.Println("   被授权软件的反馈是正确的:")
		fmt.Println("   - 这些密钥文件不是真正分离的两对密钥")
		fmt.Println("   - 需要重新生成真正独立的密钥对")
	} else {
		fmt.Println("✅ SUCCESS: The four keys are truly two separate key pairs!")
		fmt.Println("   成功: 四个密钥文件是真正分离的两对密钥对!")
		fmt.Println()
		fmt.Println("   密钥对1 (机器绑定): machine_id_decryption_private_key.pem ↔ machine_id_decryption_public_key.pem")
		fmt.Println("   密钥对2 (数字签名): signature_generation_private_key.pem ↔ signature_verification_public_key.pem")
	}
}

func loadPrivateKeyFromFile(filename string) (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}

func loadPublicKeyFromFile(filename string) (*rsa.PublicKey, error) {
	keyData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return publicKey, nil
}
