// enhanced_license_validator_v23.go
// 增强版license验证器 - 支持V23新字段（License Type和Start Date）
// 向后兼容旧版本license文件，同时支持新版本的完整功能验证

package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"runtime"
	"time"
)

// ===== 增强的数据结构定义 =====

// EnhancedLicenseData represents the enhanced license information (V23+)
// 支持新字段：license_type 和 start_date，同时向后兼容旧版本
type EnhancedLicenseData struct {
	// 基础字段（所有版本都有）
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`

	// V22+ 新增字段（可选，向后兼容）
	LicenseType *string `json:"license_type,omitempty"` // 使用指针类型支持nil值

	// V23+ 新增字段（可选，向后兼容）
	StartDate *string `json:"start_date,omitempty"` // 使用指针类型支持nil值
}

// EnhancedSignatureData represents the enhanced signature data (V23+)
// 支持新字段的签名验证，同时向后兼容旧版本
type EnhancedSignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`

	// V22+ 新增字段（可选）
	LicenseType *string `json:"t,omitempty"`

	// V23+ 新增字段（可选）
	StartUnix *int64 `json:"b,omitempty"` // "b" for begin
}

// LicenseVersion represents the detected license version
type LicenseVersion int

const (
	LicenseV1  LicenseVersion = 1  // 原始版本（无license_type和start_date）
	LicenseV22 LicenseVersion = 22 // 包含license_type
	LicenseV23 LicenseVersion = 23 // 包含license_type和start_date
)

// ===== 嵌入的密钥常量 =====

const (
	// RSA公钥 - 用于验证license签名
	EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

	// RSA私钥 - 用于解密机器ID绑定验证
	EMBEDDED_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

// ===== 增强版License验证器 =====

// EnhancedLicenseValidator 增强版license验证器
// 支持所有版本的license文件，自动检测版本并应用相应的验证逻辑
type EnhancedLicenseValidator struct {
	rsaPublicKey  *rsa.PublicKey
	rsaPrivateKey *rsa.PrivateKey
}

// NewEnhancedLicenseValidator 创建增强版license验证器
func NewEnhancedLicenseValidator() (*EnhancedLicenseValidator, error) {
	// 解析嵌入的公钥
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded public key")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded public key: %v", err)
	}

	// 解析嵌入的私钥
	privateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded private key: %v", err)
	}

	return &EnhancedLicenseValidator{
		rsaPublicKey:  publicKey,
		rsaPrivateKey: privateKey,
	}, nil
}

// DetectLicenseVersion 检测license文件版本
func (elv *EnhancedLicenseValidator) DetectLicenseVersion(license *EnhancedLicenseData) LicenseVersion {
	if license.StartDate != nil {
		return LicenseV23 // 包含start_date字段
	}
	if license.LicenseType != nil {
		return LicenseV22 // 包含license_type字段
	}
	return LicenseV1 // 原始版本
}

// ValidateEnhancedLicense 验证增强版license文件
// 自动检测版本并应用相应的验证逻辑
func (elv *EnhancedLicenseValidator) ValidateEnhancedLicense(license *EnhancedLicenseData) error {
	// 检测license版本
	version := elv.DetectLicenseVersion(license)

	fmt.Printf("🔍 Detected license version: V%d\n", int(version))

	// 1. 基础验证（所有版本都需要）
	err := elv.validateBasicFields(license)
	if err != nil {
		return fmt.Errorf("basic validation failed: %v", err)
	}

	// 2. 版本特定验证
	switch version {
	case LicenseV23:
		err = elv.validateV23Features(license)
		if err != nil {
			return fmt.Errorf("V23 validation failed: %v", err)
		}
	case LicenseV22:
		err = elv.validateV22Features(license)
		if err != nil {
			return fmt.Errorf("V22 validation failed: %v", err)
		}
	case LicenseV1:
		// 原始版本，只需要基础验证
		fmt.Println("✅ Legacy license format detected - using basic validation")
	}

	// 3. 机器绑定验证（所有版本）
	err = elv.validateMachineBinding(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("machine binding validation failed: %v", err)
	}

	// 4. 数字签名验证（版本自适应）
	err = elv.validateVersionAwareSignature(license, version)
	if err != nil {
		return fmt.Errorf("signature validation failed: %v", err)
	}

	return nil
}

// validateBasicFields 验证基础字段（所有版本通用）
func (elv *EnhancedLicenseValidator) validateBasicFields(license *EnhancedLicenseData) error {
	// 验证过期日期
	expirationDate, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("invalid expiration date format: %v", err)
	}

	if time.Now().After(expirationDate) {
		return fmt.Errorf("license has expired on %s", license.ExpirationDate)
	}

	// 验证必需字段
	if license.CompanyName == "" || license.Email == "" || license.AuthorizedSoftware == "" {
		return fmt.Errorf("missing required fields")
	}

	return nil
}

// validateV22Features 验证V22版本特性（License Type）
func (elv *EnhancedLicenseValidator) validateV22Features(license *EnhancedLicenseData) error {
	if license.LicenseType == nil {
		return fmt.Errorf("V22 license missing license_type field")
	}

	// 验证license_type值
	validTypes := []string{"lease", "demo", "perpetual"}
	licenseType := *license.LicenseType

	for _, validType := range validTypes {
		if licenseType == validType {
			fmt.Printf("✅ Valid license type: %s\n", licenseType)
			return nil
		}
	}

	return fmt.Errorf("invalid license type: %s", licenseType)
}

// validateV23Features 验证V23版本特性（Start Date + License Type）
func (elv *EnhancedLicenseValidator) validateV23Features(license *EnhancedLicenseData) error {
	// 验证V22特性
	err := elv.validateV22Features(license)
	if err != nil {
		return err
	}

	// 验证start_date字段
	if license.StartDate == nil {
		return fmt.Errorf("V23 license missing start_date field")
	}

	startDate, err := time.Parse("2006-01-02", *license.StartDate)
	if err != nil {
		return fmt.Errorf("invalid start date format: %v", err)
	}

	expirationDate, _ := time.Parse("2006-01-02", license.ExpirationDate)

	// 验证日期逻辑
	if expirationDate.Before(startDate) || expirationDate.Equal(startDate) {
		return fmt.Errorf("expiration date (%s) must be after start date (%s)",
			license.ExpirationDate, *license.StartDate)
	}

	// 检查license是否已经生效
	now := time.Now()
	if now.Before(startDate) {
		return fmt.Errorf("license is not yet active (starts on %s)", *license.StartDate)
	}

	fmt.Printf("✅ License active period: %s to %s\n", *license.StartDate, license.ExpirationDate)
	return nil
}

// validateMachineBinding 验证机器绑定（所有版本通用）
func (elv *EnhancedLicenseValidator) validateMachineBinding(encryptedMachineID string) error {
	currentMachineID, err := elv.getCurrentMachineID()
	if err != nil {
		return fmt.Errorf("failed to get current machine ID: %v", err)
	}

	licenseMachineID, err := elv.decryptMachineID(encryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt license machine ID: %v", err)
	}

	if currentMachineID != licenseMachineID {
		return fmt.Errorf("license is not valid for this machine")
	}

	return nil
}

// validateVersionAwareSignature 版本自适应的签名验证
func (elv *EnhancedLicenseValidator) validateVersionAwareSignature(license *EnhancedLicenseData, version LicenseVersion) error {
	// 解密机器ID用于签名验证
	decryptedMachineID, err := elv.decryptMachineID(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID for signature validation: %v", err)
	}

	// 根据版本构建签名数据
	var sigData EnhancedSignatureData

	// 基础字段（所有版本）
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	sigData.CompanyName = license.CompanyName
	sigData.Email = license.Email
	sigData.Software = license.AuthorizedSoftware
	sigData.Version = license.AuthorizedVersion
	sigData.ExpirationUnix = expirationTime.Unix()
	sigData.MachineIDHash = elv.hashString(decryptedMachineID)

	// 版本特定字段
	switch version {
	case LicenseV23:
		// V23包含license_type和start_date
		sigData.LicenseType = license.LicenseType
		if license.StartDate != nil {
			startTime, _ := time.Parse("2006-01-02", *license.StartDate)
			startUnix := startTime.Unix()
			sigData.StartUnix = &startUnix
		}
	case LicenseV22:
		// V22只包含license_type
		sigData.LicenseType = license.LicenseType
	case LicenseV1:
		// V1不包含新字段，保持nil
	}

	// 转换为JSON并验证签名
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	hash := sha256.Sum256(jsonData)

	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	err = rsa.VerifyPKCS1v15(elv.rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

// decryptMachineID 解密机器ID
func (elv *EnhancedLicenseValidator) decryptMachineID(encryptedMachineID string) (string, error) {
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, elv.rsaPrivateKey, encryptedData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %v", err)
	}

	return string(decryptedData), nil
}

// getCurrentMachineID 获取当前机器ID
func (elv *EnhancedLicenseValidator) getCurrentMachineID() (string, error) {
	// TODO: 实现与生成器相同的机器ID获取逻辑
	machineID := fmt.Sprintf("%s-%s-%s",
		"711221f2-c02b-4058-b6ac-165578baae25", // CPU ID
		"S9U0BB2481000104",                     // 硬盘序列号
		runtime.GOOS)                           // 操作系统
	return machineID, nil
}

// hashString 创建字符串的SHA256哈希
func (elv *EnhancedLicenseValidator) hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// LoadEnhancedLicenseFromFile 从文件加载增强版license
func LoadEnhancedLicenseFromFile(filePath string) (*EnhancedLicenseData, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read license file: %v", err)
	}

	var license EnhancedLicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license JSON: %v", err)
	}

	return &license, nil
}

// ===== 便捷函数 =====

// ValidateEnhancedLicenseFile 验证增强版license文件（便捷函数）
func ValidateEnhancedLicenseFile(licenseFilePath string) error {
	validator, err := NewEnhancedLicenseValidator()
	if err != nil {
		return fmt.Errorf("failed to create validator: %v", err)
	}

	license, err := LoadEnhancedLicenseFromFile(licenseFilePath)
	if err != nil {
		return fmt.Errorf("failed to load license: %v", err)
	}

	err = validator.ValidateEnhancedLicense(license)
	if err != nil {
		return fmt.Errorf("license validation failed: %v", err)
	}

	return nil
}

// ===== 使用示例 =====

// ExampleEnhancedUsage 增强版license验证使用示例
func ExampleEnhancedUsage() {
	fmt.Println("=== 增强版License验证器示例 ===")

	// 方法1：使用便捷函数（推荐）
	err := ValidateEnhancedLicenseFile("license.json")
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
		return
	}
	fmt.Println("✅ License验证成功")

	// 方法2：详细验证过程
	validator, err := NewEnhancedLicenseValidator()
	if err != nil {
		fmt.Printf("创建验证器失败: %v\n", err)
		return
	}

	license, err := LoadEnhancedLicenseFromFile("license.json")
	if err != nil {
		fmt.Printf("加载license失败: %v\n", err)
		return
	}

	// 显示license信息
	version := validator.DetectLicenseVersion(license)
	fmt.Printf("\n📋 License信息:\n")
	fmt.Printf("  版本: V%d\n", int(version))
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)

	if license.LicenseType != nil {
		fmt.Printf("  类型: %s\n", *license.LicenseType)
	}

	if license.StartDate != nil {
		fmt.Printf("  开始: %s\n", *license.StartDate)
	}

	fmt.Printf("  过期: %s\n", license.ExpirationDate)

	// 验证license
	err = validator.ValidateEnhancedLicense(license)
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
		return
	}

	fmt.Println("✅ License验证通过，软件功能已解锁")
}

/*
===== 升级指南 =====

## 从旧版本验证器升级到增强版

### 1. 替换验证器文件
将现有的 standalone_license_validator.go 替换为此文件

### 2. 更新函数调用
旧版本:
    err := ValidateLicenseFile("license.json")

新版本:
    err := ValidateEnhancedLicenseFile("license.json")

### 3. 向后兼容性
✅ 自动检测license版本
✅ 支持V1（原始）、V22（+license_type）、V23（+start_date）
✅ 无需修改现有license文件
✅ 新功能自动启用

### 4. 新功能特性
- License Type验证（lease/demo/perpetual）
- Start Date验证（license生效时间）
- 版本自适应签名验证
- 增强的错误信息

### 5. 集成示例
```go
func main() {
    // 验证license（自动检测版本）
    err := ValidateEnhancedLicenseFile("license.json")
    if err != nil {
        log.Fatal("License验证失败:", err)
    }

    fmt.Println("软件已授权，正在启动...")
    // 继续运行软件
}
```

===== 版本兼容性矩阵 =====

| License版本 | license_type | start_date | 验证结果 |
|------------|-------------|------------|----------|
| V1 (旧版)   | ❌          | ❌         | ✅ 兼容  |
| V22        | ✅          | ❌         | ✅ 支持  |
| V23        | ✅          | ✅         | ✅ 完整  |

===== 部署建议 =====

1. **渐进式升级**: 先部署增强版验证器，再升级license生成器
2. **测试验证**: 使用现有license文件测试兼容性
3. **功能启用**: 新license自动启用增强功能
4. **监控日志**: 观察版本检测和验证结果

*/
