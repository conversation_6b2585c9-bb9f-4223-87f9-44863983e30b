# License Signature Construction Summary
# 许可证签名构建摘要

## 🎯 Quick Overview / 快速概述

The license signature is constructed using a **compact JSON structure** that is **SHA256 hashed** and **RSA signed**.

许可证签名使用**紧凑的JSON结构**构建，经过**SHA256哈希**和**RSA签名**。

## 📋 Signature Data Structure / 签名数据结构

```go
type SignatureData struct {
    CompanyName    string `json:"c"` // 公司名称
    Email          string `json:"e"` // 邮箱地址
    Software       string `json:"s"` // 软件名称
    Version        string `json:"v"` // 软件版本
    ExpirationUnix int64  `json:"x"` // 过期时间戳
    MachineIDHash  string `json:"m"` // 机器ID哈希
}
```

## 🔧 Construction Process / 构建过程

### 1. Data Collection / 数据收集
```go
sigData := SignatureData{
    CompanyName:    "gwm2",                                    // 从license数据
    Email:          "<EMAIL>",                           // 从license数据
    Software:       "LS-DYNA Model License Generate Factory", // 从license数据
    Version:        "2.3.0",                                 // 从license数据
    ExpirationUnix: **********,                              // 2025-08-10转Unix时间戳
    MachineIDHash:  "jKl9mN2pQ3rS",                          // SHA256(machineID)[:16]
}
```

### 2. JSON Serialization / JSON序列化
```json
{"c":"gwm2","e":"<EMAIL>","s":"LS-DYNA Model License Generate Factory","v":"2.3.0","x":**********,"m":"jKl9mN2pQ3rS"}
```

### 3. Hash and Sign / 哈希和签名
```go
jsonData := json.Marshal(sigData)           // JSON字节
hash := sha256.Sum256(jsonData)             // SHA256哈希
signature := rsa.SignPKCS1v15(privateKey, crypto.SHA256, hash) // RSA签名
signatureBase64 := base64.StdEncoding.EncodeToString(signature) // Base64编码
```

## 🔍 Key Features / 关键特性

### 1. Compact Design / 紧凑设计
- **Shortened JSON keys**: `c`, `e`, `s`, `v`, `x`, `m` (减少数据大小)
- **Unix timestamp**: 数字格式比日期字符串更紧凑
- **Hashed machine ID**: 64+字符减少到16字符
- **Total size**: 通常150-200字节，适合RSA-2048

### 2. Security Elements / 安全元素
- **Company binding**: 公司名称和邮箱
- **Software binding**: 特定软件名称和版本
- **Time binding**: 过期时间戳
- **Machine binding**: 哈希的机器ID
- **Integrity protection**: RSA数字签名

### 3. Machine ID Hashing / 机器ID哈希
```go
func hashString(input string) string {
    hash := sha256.Sum256([]byte(input))                    // SHA256哈希
    encoded := base64.StdEncoding.EncodeToString(hash[:])   // Base64编码
    return encoded[:16]                                     // 取前16字符
}

// 示例:
// Input:  "711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104"
// Output: "jKl9mN2pQ3rS" (前16字符)
```

## 📊 Real Example / 实际示例

### Input Data / 输入数据
```
Company: "gwm2"
Email: "<EMAIL>"
Software: "LS-DYNA Model License Generate Factory"
Version: "2.3.0"
Expiration: "2025-08-10" → Unix: **********
Machine ID: "711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104"
```

### Signature Data JSON / 签名数据JSON
```json
{
  "c": "gwm2",
  "e": "<EMAIL>",
  "s": "LS-DYNA Model License Generate Factory",
  "v": "2.3.0",
  "x": **********,
  "m": "jKl9mN2pQ3rS"
}
```

### Final Signature / 最终签名
```
Base64 encoded RSA signature (344 characters):
"h0/mo+4yQupr7QmbZCr7w80c4JwFSMvxFp7FlqsUTUaF0s59PYAw0j63yb26OLUi..."
```

## 🔒 Verification Process / 验证过程

验证时需要**完全相同的过程**：

1. **重构SignatureData** - 使用相同的字段和格式
2. **JSON序列化** - 使用相同的JSON编组
3. **SHA256哈希** - 对JSON字节进行哈希
4. **RSA验证** - 使用公钥验证签名

```go
// 验证伪代码
sigData := reconstructSignatureData(license, decryptedMachineID)
jsonData := json.Marshal(sigData)
hash := sha256.Sum256(jsonData)
signature := base64.StdEncoding.DecodeString(license.Signature)
err := rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
// err == nil 表示签名有效
```

## ⚠️ Critical Requirements / 关键要求

### For Signature Generation / 签名生成
- ✅ 使用正确的私钥
- ✅ 包含所有必要字段
- ✅ 正确的机器ID哈希算法
- ✅ Unix时间戳转换

### For Signature Verification / 签名验证
- ✅ 完全相同的SignatureData结构
- ✅ 相同的JSON序列化方式
- ✅ 相同的机器ID哈希算法
- ✅ 正确的公钥用于验证

## 🎯 Design Benefits / 设计优势

1. **Security / 安全性**
   - 包含所有关键许可证信息
   - 防止篡改和伪造
   - 机器绑定和时间限制

2. **Efficiency / 效率**
   - 紧凑的数据结构
   - 适合RSA-2048限制
   - 快速验证过程

3. **Reliability / 可靠性**
   - 标准的加密算法
   - 明确的数据格式
   - 一致的实现方式

## 📚 Related Files / 相关文件

- `SIGNATURE_DATA_CONSTRUCTION_GUIDE.md` - 详细构建指南
- `signature_construction_example.go` - 完整示例代码
- `crypto.go` - 实际实现代码
- `standalone_license_validator.go` - 验证实现

---

**This signature construction ensures both security and compatibility across the license system.**

**此签名构建确保了许可证系统的安全性和兼容性。**
