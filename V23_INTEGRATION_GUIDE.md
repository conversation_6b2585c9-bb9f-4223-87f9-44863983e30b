# V23 License Validator Integration Guide
# V23许可证验证器集成指南

## 🎯 Overview / 概述

This guide shows how to integrate the V23 License Validator into your authorized software. The V23 validator is specifically designed for the new license format with License Type and Start Date fields.

本指南展示如何将V23许可证验证器集成到您的被授权软件中。V23验证器专门为包含许可证类型和开始日期字段的新许可证格式设计。

## 🚀 Quick Integration / 快速集成

### Step 1: Copy Validator File / 第1步：复制验证器文件

```bash
# Copy the V23 validator to your project
cp V23_LICENSE_VALIDATOR.go your_project/license_validator.go
```

### Step 2: Update Package Name / 第2步：更新包名

```go
// Change the first line from:
package main

// To your package name:
package yourpackage
```

### Step 3: Basic Integration / 第3步：基础集成

```go
package main

import (
    "fmt"
    "log"
    "os"
)

func main() {
    fmt.Println("🚀 Starting your software...")

    // Validate V23 license
    err := ValidateV23LicenseFile("license.json")
    if err != nil {
        log.Printf("❌ License validation failed: %v", err)
        fmt.Println("Software cannot start. Please check your license file.")
        os.Exit(1)
    }

    fmt.Println("✅ License validation successful")
    fmt.Println("🎉 Software is authorized, starting...")

    // Continue with your software logic
    startYourApplication()
}

func startYourApplication() {
    // Your application logic here
    fmt.Println("Application is running...")
}
```

## 📋 V23 License Format / V23许可证格式

### Required Fields / 必需字段

The V23 license file must contain all these fields:

V23许可证文件必须包含所有这些字段：

```json
{
  "company_name": "Your Company Name",
  "email": "<EMAIL>",
  "phone": "************",
  "authorized_software": "Your Software Name",
  "authorized_version": "1.0.0",
  "license_type": "lease",              // NEW: lease/demo/perpetual
  "start_date": "2025-07-12",           // NEW: YYYY-MM-DD format
  "expiration_date": "2026-07-12",      // YYYY-MM-DD format
  "issued_date": "2025-07-11",          // YYYY-MM-DD format
  "encrypted_machine_id": "base64...",  // Encrypted machine binding
  "signature": "base64..."              // Digital signature
}
```

### License Types / 许可证类型

| Type | Description | Use Case |
|------|-------------|----------|
| **lease** | Time-limited license | Standard commercial licensing |
| **demo** | Trial/evaluation license | Software evaluation |
| **perpetual** | Permanent license | One-time purchase |

| 类型 | 描述 | 使用场景 |
|------|------|----------|
| **lease** | 限时许可证 | 标准商业许可 |
| **demo** | 试用/评估许可证 | 软件评估 |
| **perpetual** | 永久许可证 | 一次性购买 |

## 🔧 Advanced Integration / 高级集成

### Detailed Validation with Information Display / 带信息显示的详细验证

```go
func validateLicenseWithDetails() error {
    // Create validator
    validator, err := NewV23LicenseValidator()
    if err != nil {
        return fmt.Errorf("failed to create validator: %v", err)
    }

    // Load license
    license, err := LoadV23LicenseFromFile("license.json")
    if err != nil {
        return fmt.Errorf("failed to load license: %v", err)
    }

    // Display license information
    fmt.Printf("\n📋 License Information:\n")
    fmt.Printf("  Company: %s\n", license.CompanyName)
    fmt.Printf("  Email: %s\n", license.Email)
    fmt.Printf("  Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
    fmt.Printf("  Type: %s\n", license.LicenseType)
    fmt.Printf("  Active Period: %s to %s\n", license.StartDate, license.ExpirationDate)
    fmt.Printf("  Issued: %s\n", license.IssuedDate)

    // Validate license
    err = validator.ValidateV23License(license)
    if err != nil {
        return fmt.Errorf("license validation failed: %v", err)
    }

    return nil
}
```

### Periodic License Validation / 定期许可证验证

```go
import "time"

func startPeriodicValidation() {
    // Validate license every hour
    ticker := time.NewTicker(1 * time.Hour)
    
    go func() {
        for range ticker.C {
            err := ValidateV23LicenseFile("license.json")
            if err != nil {
                log.Printf("⚠️ Periodic license validation failed: %v", err)
                // Handle license failure (e.g., graceful shutdown)
                handleLicenseFailure(err)
            } else {
                log.Println("✅ Periodic license validation successful")
            }
        }
    }()
}

func handleLicenseFailure(err error) {
    // Implement your license failure handling logic
    log.Printf("License failure detected: %v", err)
    // Could trigger graceful shutdown, disable features, etc.
}
```

### License Type-Based Feature Control / 基于许可证类型的功能控制

```go
func enableFeaturesBasedOnLicenseType() error {
    validator, err := NewV23LicenseValidator()
    if err != nil {
        return err
    }

    license, err := LoadV23LicenseFromFile("license.json")
    if err != nil {
        return err
    }

    // Validate first
    err = validator.ValidateV23License(license)
    if err != nil {
        return err
    }

    // Enable features based on license type
    switch license.LicenseType {
    case "perpetual":
        enableAllFeatures()
        fmt.Println("🎉 All features enabled (Perpetual License)")
        
    case "lease":
        enableStandardFeatures()
        fmt.Println("✅ Standard features enabled (Lease License)")
        
    case "demo":
        enableDemoFeatures()
        fmt.Println("🔍 Demo features enabled (Demo License)")
        
    default:
        return fmt.Errorf("unknown license type: %s", license.LicenseType)
    }

    return nil
}

func enableAllFeatures() {
    // Enable all software features
}

func enableStandardFeatures() {
    // Enable standard commercial features
}

func enableDemoFeatures() {
    // Enable limited demo features
}
```

## 🔍 Error Handling / 错误处理

### Common Error Types / 常见错误类型

```go
func handleValidationErrors(err error) {
    if err == nil {
        return
    }

    errorMsg := err.Error()
    
    switch {
    case strings.Contains(errorMsg, "required"):
        fmt.Println("❌ License file is missing required fields")
        
    case strings.Contains(errorMsg, "expired"):
        fmt.Println("❌ License has expired")
        
    case strings.Contains(errorMsg, "not yet active"):
        fmt.Println("❌ License is not yet active")
        
    case strings.Contains(errorMsg, "machine"):
        fmt.Println("❌ License is not valid for this machine")
        
    case strings.Contains(errorMsg, "signature"):
        fmt.Println("❌ License signature verification failed")
        
    case strings.Contains(errorMsg, "license type"):
        fmt.Println("❌ Invalid license type")
        
    default:
        fmt.Printf("❌ License validation failed: %v\n", err)
    }
}
```

### Graceful Error Recovery / 优雅的错误恢复

```go
func startWithLicenseValidation() {
    maxRetries := 3
    retryDelay := 5 * time.Second

    for i := 0; i < maxRetries; i++ {
        err := ValidateV23LicenseFile("license.json")
        if err == nil {
            fmt.Println("✅ License validation successful")
            startApplication()
            return
        }

        fmt.Printf("⚠️ License validation attempt %d failed: %v\n", i+1, err)
        
        if i < maxRetries-1 {
            fmt.Printf("Retrying in %v...\n", retryDelay)
            time.Sleep(retryDelay)
        }
    }

    fmt.Println("❌ License validation failed after all retries")
    os.Exit(1)
}
```

## 🛠️ Troubleshooting / 故障排除

### Debug Mode / 调试模式

```go
func debugLicenseValidation() {
    fmt.Println("🔍 Debug: Starting license validation...")

    // Check if license file exists
    if _, err := os.Stat("license.json"); os.IsNotExist(err) {
        fmt.Println("❌ Debug: License file 'license.json' not found")
        return
    }
    fmt.Println("✅ Debug: License file found")

    // Try to load license
    license, err := LoadV23LicenseFromFile("license.json")
    if err != nil {
        fmt.Printf("❌ Debug: Failed to load license: %v\n", err)
        return
    }
    fmt.Println("✅ Debug: License loaded successfully")

    // Check required fields
    fmt.Printf("🔍 Debug: Company: %s\n", license.CompanyName)
    fmt.Printf("🔍 Debug: Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
    fmt.Printf("🔍 Debug: License Type: %s\n", license.LicenseType)
    fmt.Printf("🔍 Debug: Period: %s to %s\n", license.StartDate, license.ExpirationDate)

    // Validate step by step
    validator, err := NewV23LicenseValidator()
    if err != nil {
        fmt.Printf("❌ Debug: Failed to create validator: %v\n", err)
        return
    }

    err = validator.ValidateV23License(license)
    if err != nil {
        fmt.Printf("❌ Debug: Validation failed: %v\n", err)
        return
    }

    fmt.Println("✅ Debug: All validations passed!")
}
```

### Common Issues / 常见问题

**Issue: "failed to read license file"**
```
Solution: Check if license.json exists in the correct path
Command: ls -la license.json
```

**Issue: "failed to parse license JSON"**
```
Solution: Validate JSON format
Command: cat license.json | jq .
```

**Issue: "license has expired"**
```
Solution: Check expiration_date field and request new license
```

**Issue: "license is not yet active"**
```
Solution: Check start_date field - license may be scheduled for future activation
```

**Issue: "machine binding validation failed"**
```
Solution: License is bound to different machine - request new license for current machine
```

## 📦 Deployment Considerations / 部署考虑

### File Placement / 文件放置

```
your_application/
├── your_application.exe
├── license.json              ← License file
├── license_validator.go      ← Validator (if source)
└── config/
    └── app_config.json
```

### Security Best Practices / 安全最佳实践

1. **Protect License File / 保护许可证文件**
   - Set appropriate file permissions
   - Consider encryption at rest
   - Monitor file access

2. **Validate Early / 早期验证**
   - Validate license at application startup
   - Fail fast if license is invalid
   - Provide clear error messages

3. **Periodic Checks / 定期检查**
   - Implement periodic validation
   - Handle license expiration gracefully
   - Log validation events

## 🎯 Best Practices / 最佳实践

### Integration Checklist / 集成检查清单

- [ ] Copy V23_LICENSE_VALIDATOR.go to your project
- [ ] Update package name to match your project
- [ ] Implement basic validation at startup
- [ ] Add error handling for all validation scenarios
- [ ] Test with valid V23 license file
- [ ] Test error scenarios (expired, wrong machine, etc.)
- [ ] Implement license type-based feature control
- [ ] Add periodic validation if needed
- [ ] Document license requirements for users

### Performance Tips / 性能提示

- Cache validator instance if validating frequently
- Use goroutines for periodic validation
- Implement timeout for validation operations
- Log validation results for monitoring

---

**🎉 Congratulations!** You now have a complete V23 license validation system integrated into your software.

**🎉 恭喜！** 您现在已经将完整的V23许可证验证系统集成到您的软件中。
