// 验证现有许可证文件的独立程序
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// 加载私钥
func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return privateKey, nil
}

// 验证许可证签名
func validateLicense(licenseFile string) error {
	fmt.Printf("🔍 Validating License: %s\n", licenseFile)
	fmt.Println("=" + fmt.Sprintf("%*s", len(licenseFile)+20, "="))

	// 1. 加载许可证文件
	fmt.Println("\n📂 Step 1: Loading License File...")
	licenseData, err := os.ReadFile(licenseFile)
	if err != nil {
		return fmt.Errorf("failed to read license file: %v", err)
	}

	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		return fmt.Errorf("failed to parse license: %v", err)
	}

	fmt.Printf("✅ License loaded successfully\n")
	fmt.Printf("   Company: %s\n", license.CompanyName)
	fmt.Printf("   Email: %s\n", license.Email)
	fmt.Printf("   Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("   Expiration: %s\n", license.ExpirationDate)
	fmt.Printf("   Issued: %s\n", license.IssuedDate)

	// 2. 加载私钥
	fmt.Println("\n🔑 Step 2: Loading Private Key...")
	privateKey, err := loadPrivateKey()
	if err != nil {
		return fmt.Errorf("failed to load private key: %v", err)
	}
	fmt.Println("✅ Private key loaded successfully")

	// 3. 解密机器ID
	fmt.Println("\n🔓 Step 3: Decrypting Machine ID...")
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	fmt.Printf("✅ Machine ID decrypted: %s\n", string(decryptedMachineID))

	// 4. 重构签名数据
	fmt.Println("\n📋 Step 4: Reconstructing Signature Data...")
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}

	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}

	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("✅ Signature data reconstructed:\n")
	fmt.Printf("   JSON: %s\n", string(jsonData))
	fmt.Printf("   Company: %s\n", sigData.CompanyName)
	fmt.Printf("   Email: %s\n", sigData.Email)
	fmt.Printf("   Software: %s\n", sigData.Software)
	fmt.Printf("   Version: %s\n", sigData.Version)
	fmt.Printf("   Expiration Unix: %d\n", sigData.ExpirationUnix)
	fmt.Printf("   Machine ID Hash: %s\n", sigData.MachineIDHash)

	// 5. 验证签名
	fmt.Println("\n🔐 Step 5: Verifying Digital Signature...")
	hash := sha256.Sum256(jsonData)

	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ Signature verification failed: %v\n", err)
		fmt.Printf("   Expected hash: %x\n", hash)
		fmt.Printf("   Signature length: %d bytes\n", len(signature))
		return fmt.Errorf("signature verification failed: %v", err)
	}

	fmt.Println("✅ Signature verification successful!")

	// 6. 检查过期时间
	fmt.Println("\n📅 Step 6: Checking Expiration...")
	if time.Now().After(expirationTime) {
		fmt.Printf("⚠️  License has expired on %s\n", license.ExpirationDate)
	} else {
		fmt.Printf("✅ License is valid until %s\n", license.ExpirationDate)
	}

	return nil
}

func main() {
	fmt.Println("🚀 License Validation Test")
	fmt.Println("==========================")

	// 测试当前的factory_license.json
	if _, err := os.Stat("factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing current factory_license.json...")
		err := validateLicense("factory_license.json")
		if err != nil {
			fmt.Printf("❌ Current license validation failed: %v\n", err)
		} else {
			fmt.Println("\n🎉 Current license validation successful!")
		}
	} else {
		fmt.Println("⚠️  factory_license.json not found")
	}

	// 测试fresh_factory_license.json（如果存在）
	if _, err := os.Stat("fresh_factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing fresh_factory_license.json...")
		err := validateLicense("fresh_factory_license.json")
		if err != nil {
			fmt.Printf("❌ Fresh license validation failed: %v\n", err)
		} else {
			fmt.Println("\n🎉 Fresh license validation successful!")
		}
	}

	// 测试test_factory_license.json（如果存在）
	if _, err := os.Stat("test_factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing test_factory_license.json...")
		err := validateLicense("test_factory_license.json")
		if err != nil {
			fmt.Printf("❌ Test license validation failed: %v\n", err)
		} else {
			fmt.Println("\n🎉 Test license validation successful!")
		}
	}

	fmt.Println("\n📋 Validation Summary:")
	fmt.Println("- If validation succeeds, the license signature is correct")
	fmt.Println("- If validation fails, there may be a signature mismatch issue")
	fmt.Println("- This simulates what authorized software would do")
}
