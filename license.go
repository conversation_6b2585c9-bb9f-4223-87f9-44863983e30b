package main

import (
	"crypto/rsa"
	"fmt"
	"time"
)

// LicenseGenerator handles the core license generation logic
// Uses separate keys for different purposes:
// - machineDecryptionKey: for decrypting machine ID (unchanged)
// - signingKey: for creating digital signatures (separate key)
// - companyIDKey: for encrypting company ID (new separate key)
type LicenseGenerator struct {
	machineInfo          *MachineInfo
	machineDecryptionKey *rsa.PrivateKey // For machine ID decryption
	signingKey           *rsa.PrivateKey // For digital signatures
	companyIDKey         *rsa.PrivateKey // For company ID encryption
}

// NewLicenseGenerator creates a new license generator instance with separate keys
func NewLicenseGenerator(machineInfoPath, machineDecryptionKeyPath, signingKeyPath, companyIDKeyPath string) (*LicenseGenerator, error) {
	// Load machine info
	machineInfo, err := LoadMachineInfo(machineInfoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load machine info: %v", err)
	}

	// Load machine decryption key (unchanged - for machine ID decryption)
	machineDecryptionKey, err := LoadPrivateKey(machineDecryptionKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load machine decryption key: %v", err)
	}

	// Load signing key (new - for digital signatures)
	signingKey, err := LoadPrivateKey(signingKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load signing key: %v", err)
	}

	// Load company ID key (new - for company ID encryption)
	companyIDKey, err := LoadCompanyIDPrivateKey(companyIDKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load company ID key: %v", err)
	}

	return &LicenseGenerator{
		machineInfo:          machineInfo,
		machineDecryptionKey: machineDecryptionKey,
		signingKey:           signingKey,
		companyIDKey:         companyIDKey,
	}, nil
}

// GenerateLicense creates a new license with the provided parameters
func (lg *LicenseGenerator) GenerateLicense(
	companyName, email, phone, software, version, licenseType, companyID string,
	startDate, expirationDate time.Time,
) (*LicenseData, error) {
	// Get the raw machine ID (decrypt if necessary) for signature creation
	// Use machine decryption key for this purpose
	rawMachineID, err := GetRawMachineID(lg.machineInfo, lg.machineDecryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get raw machine ID: %v", err)
	}

	// Encrypt company ID using company ID key
	encryptedCompanyID, err := EncryptCompanyID(companyID, lg.companyIDKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt company ID: %v", err)
	}

	// Create license data
	license := &LicenseData{
		CompanyName:        companyName,
		Email:              email,
		Phone:              phone,
		AuthorizedSoftware: software,
		AuthorizedVersion:  version,
		LicenseType:        licenseType,
		StartDate:          startDate.Format("2006-01-02"),
		ExpirationDate:     expirationDate.Format("2006-01-02"),
		IssuedDate:         time.Now().Format("2006-01-02"),
		CompanyID:          companyID,
		EncryptedCompanyID: encryptedCompanyID,
	}

	// Use the SAME encrypted machine ID as in the machine info file (hybrid approach)
	// This allows users to verify the license is for their machine
	// while using raw machine ID for signature creation
	license.EncryptedMachineID = GetEncryptedMachineID(lg.machineInfo)

	// Create signature using the RAW machine ID and company ID (for correct verification)
	// Parse the start and expiration dates from the license to ensure consistency
	licenseStartTime, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license start date: %v", err)
	}

	licenseExpirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license expiration date: %v", err)
	}

	// Use separate signing key for digital signature creation
	signature, err := CreateSignature(license, lg.signingKey, licenseStartTime, licenseExpirationTime, rawMachineID, companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to create signature: %v", err)
	}

	license.Signature = signature

	return license, nil
}

// ValidateLicenseInputs validates the input parameters for license generation
func ValidateLicenseInputs(companyName, email, phone, software, version string, expirationDate time.Time) error {
	if companyName == "" {
		return fmt.Errorf("company name is required")
	}
	if email == "" {
		return fmt.Errorf("email is required")
	}
	if phone == "" {
		return fmt.Errorf("phone is required")
	}
	if software == "" {
		return fmt.Errorf("authorized software is required")
	}
	if version == "" {
		return fmt.Errorf("version is required")
	}
	if expirationDate.Before(time.Now()) {
		return fmt.Errorf("expiration date must be in the future")
	}

	return nil
}

// CalculateExpirationDate calculates expiration date based on preset
func CalculateExpirationDate(preset ExpirationPreset) time.Time {
	now := time.Now()
	return now.Add(preset.Duration).Add(preset.Extra)
}

// FormatLicenseForDisplay formats license data for display in UI
func FormatLicenseForDisplay(license *LicenseData) string {
	return fmt.Sprintf(`License Information:
Company: %s
Email: %s
Phone: %s
Software: %s
Version: %s
Expiration: %s
Encrypted Machine ID: %s
Issued: %s
Signature: %s...`,
		license.CompanyName,
		license.Email,
		license.Phone,
		license.AuthorizedSoftware, // Software name only
		license.AuthorizedVersion,  // Software version only
		license.ExpirationDate,     // Already in YYYY-MM-DD format
		license.EncryptedMachineID[:min(len(license.EncryptedMachineID), 30)]+"...", // Show encrypted machine ID
		license.IssuedDate, // Already in YYYY-MM-DD format (renamed from generated_date)
		license.Signature[:min(len(license.Signature), 20)])
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
