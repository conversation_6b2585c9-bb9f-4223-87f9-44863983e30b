# Company ID Feature Guide
# 公司ID功能使用指南

## Overview / 概述

Version 27 introduces a new Company ID feature that assigns unique 7-digit identifiers to companies for enhanced license management and security.

版本27引入了新的公司ID功能，为公司分配唯一的7位数字标识符，以增强许可证管理和安全性。

## New Features / 新功能

### 1. Company ID Input Field / 公司ID输入框

- **Location**: License Details section in the GUI
- **Format**: `XXX-XXXX` (e.g., `123-4567`)
- **Auto-formatting**: Automatically formats input as you type
- **Validation**: Real-time validation of format and uniqueness

**位置**：GUI界面的许可证详情部分
**格式**：`XXX-XXXX`（例如：`123-4567`）
**自动格式化**：输入时自动格式化
**验证**：实时格式和唯一性验证

### 2. Automatic Company Registry / 自动公司注册表

- **File**: `company_registry.json`
- **Auto-generation**: Automatically assigns IDs starting from `100-0000`
- **Duplicate detection**: Prevents duplicate company IDs
- **Company lookup**: Recognizes existing companies by name

**文件**：`company_registry.json`
**自动生成**：从`100-0000`开始自动分配ID
**重复检测**：防止重复的公司ID
**公司查找**：通过名称识别现有公司

### 3. Enhanced Security / 增强安全性

- **Separate encryption**: Company IDs use dedicated RSA-2048 encryption
- **Signature inclusion**: Company ID hash included in digital signature
- **Key separation**: Independent key pair for company ID encryption

**独立加密**：公司ID使用专用的RSA-2048加密
**签名包含**：公司ID哈希包含在数字签名中
**密钥分离**：公司ID加密使用独立的密钥对

## How to Use / 使用方法

### Method 1: Automatic Assignment / 方法1：自动分配

1. Leave the Company ID field empty
2. The system will automatically assign a new ID or find existing one
3. Based on company name from machine info file

1. 保持公司ID字段为空
2. 系统将自动分配新ID或查找现有ID
3. 基于机器信息文件中的公司名称

### Method 2: Manual Assignment / 方法2：手动分配

1. Enter a 7-digit ID in format `XXX-XXXX`
2. System validates format and uniqueness
3. If valid, assigns the ID to the company

1. 输入格式为`XXX-XXXX`的7位数字ID
2. 系统验证格式和唯一性
3. 如果有效，将ID分配给公司

### Input Guidelines / 输入指南

- **Valid formats**: `123-4567`, `100-0000`, `999-9999`
- **Invalid formats**: `12-3456`, `1234-567`, `abc-defg`
- **Auto-formatting**: Type `1234567` → automatically becomes `123-4567`

**有效格式**：`123-4567`、`100-0000`、`999-9999`
**无效格式**：`12-3456`、`1234-567`、`abc-defg`
**自动格式化**：输入`1234567` → 自动变成`123-4567`

## File Structure / 文件结构

### New Files / 新文件

```
company_registry.json                    # Company ID registry
company_id_encryption_private_key.pem   # Company ID encryption key
company_id_encryption_public_key.pem    # Company ID decryption key (for validator)
```

### Updated License Format / 更新的许可证格式

```json
{
  "company_name": "Example Company",
  "email": "<EMAIL>",
  "phone": "18888888888",
  "company_id": "123-4567",
  "encrypted_company_id": "base64_encrypted_data...",
  "authorized_software": "Software Name",
  "authorized_version": "1.0.0",
  "license_type": "lease",
  "start_date": "2025-07-13",
  "expiration_date": "2026-01-13",
  "issued_date": "2025-07-13",
  "encrypted_machine_id": "base64_encrypted_data...",
  "signature": "base64_signature_data..."
}
```

## Company Registry Management / 公司注册表管理

### Registry Structure / 注册表结构

```json
{
  "version": "1.0",
  "last_updated": "2025-07-13T10:30:00Z",
  "next_available_id": 1000001,
  "companies": [
    {
      "company_id": "100-0000",
      "company_name": "Example Company",
      "email": "<EMAIL>",
      "phone": "18888888888",
      "created_date": "2025-07-13",
      "last_used": "2025-07-13",
      "notes": ""
    }
  ]
}
```

### Backup Recommendations / 备份建议

1. **Regular backups**: Backup `company_registry.json` regularly
2. **Key security**: Keep encryption keys secure
3. **Version control**: Track changes to the registry

1. **定期备份**：定期备份`company_registry.json`
2. **密钥安全**：保护加密密钥安全
3. **版本控制**：跟踪注册表的变更

## Compilation / 编译

### Updated Build Command / 更新的编译命令

```bash
go build -o license-generator-v27-with-company-id.exe main.go models.go crypto.go license.go company_registry.go
```

### Required Files / 必需文件

- `main.go` - GUI application
- `models.go` - Data structures
- `crypto.go` - Cryptographic functions
- `license.go` - License generation logic
- `company_registry.go` - Company registry management

## Migration from V26 / 从V26版本迁移

### Automatic Migration / 自动迁移

- Existing licenses remain valid
- New licenses include company ID
- Registry is created automatically

现有许可证保持有效
新许可证包含公司ID
注册表自动创建

### Key Generation / 密钥生成

Run the key generator to create company ID encryption keys:

运行密钥生成器创建公司ID加密密钥：

```bash
go run generate_company_id_keys.go crypto.go models.go company_registry.go
```

## Troubleshooting / 故障排除

### Common Issues / 常见问题

1. **"Company ID already exists"**: Use a different ID or check existing records
2. **"Invalid format"**: Ensure ID follows `XXX-XXXX` format
3. **"Key file not found"**: Generate company ID encryption keys first

1. **"公司ID已存在"**：使用不同的ID或检查现有记录
2. **"格式无效"**：确保ID遵循`XXX-XXXX`格式
3. **"找不到密钥文件"**：首先生成公司ID加密密钥

### Support / 支持

For technical support, refer to:
- `COMPANY_ID_MAPPING.md` - Detailed mapping documentation
- `company_registry.json` - Current company records
- Application logs for error details

技术支持请参考：
- `COMPANY_ID_MAPPING.md` - 详细映射文档
- `company_registry.json` - 当前公司记录
- 应用程序日志获取错误详情

---

**Version**: 27  
**Last Updated**: 2025-07-13  
**Compatibility**: Backward compatible with V26 and earlier
