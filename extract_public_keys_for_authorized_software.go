package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"log"
	"os"
)

// ExtractPublicKeyFromPrivate extracts public key from private key file
func ExtractPublicKeyFromPrivate(privateKeyFile string) (string, error) {
	// Read private key file
	data, err := ioutil.ReadFile(privateKeyFile)
	if err != nil {
		return "", fmt.Errorf("failed to read private key file: %v", err)
	}

	// Parse private key
	block, _ := pem.Decode(data)
	if block == nil {
		return "", fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	// Extract public key
	publicKey := &privateKey.PublicKey

	// Convert to PKIX format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to marshal public key: %v", err)
	}

	// Create PEM block
	publicKeyPEM := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	}

	// Encode to string
	return string(pem.EncodeToMemory(publicKeyPEM)), nil
}

// ReadPublicKeyFile reads public key from file
func ReadPublicKeyFile(publicKeyFile string) (string, error) {
	data, err := ioutil.ReadFile(publicKeyFile)
	if err != nil {
		return "", fmt.Errorf("failed to read public key file: %v", err)
	}
	return string(data), nil
}

func main() {
	fmt.Println("🔑 Public Key Extractor for Authorized Software")
	fmt.Println("===============================================")
	fmt.Println("This tool extracts the three public keys needed for V27 license validation.")
	fmt.Println()

	// Define key files
	keyFiles := map[string]string{
		"Machine ID Decryption":  "machine_decryption_private_key_to_decryp_factory_machineinfo.pem",
		"Signature Verification": "license_signing_private_key.pem",
		"Company ID Decryption":  "company_id_encryption_private_key.pem",
	}

	publicKeys := make(map[string]string)

	// Extract public keys from private keys
	for keyName, privateKeyFile := range keyFiles {
		fmt.Printf("📂 Processing %s...\n", keyName)

		if _, err := os.Stat(privateKeyFile); os.IsNotExist(err) {
			fmt.Printf("   ❌ Private key file not found: %s\n", privateKeyFile)
			continue
		}

		publicKeyPEM, err := ExtractPublicKeyFromPrivate(privateKeyFile)
		if err != nil {
			fmt.Printf("   ❌ Failed to extract public key: %v\n", err)
			continue
		}

		publicKeys[keyName] = publicKeyPEM
		fmt.Printf("   ✅ Public key extracted successfully\n")
	}

	// Also try to read existing public key files
	publicKeyFiles := map[string]string{
		"Machine ID Decryption":  "machine_id_decryption_public_key.pem",
		"Signature Verification": "signature_verification_public_key.pem",
		"Company ID Decryption":  "company_id_encryption_public_key.pem",
	}

	for keyName, publicKeyFile := range publicKeyFiles {
		if _, err := os.Stat(publicKeyFile); err == nil {
			fmt.Printf("📂 Reading existing %s public key...\n", keyName)
			publicKeyPEM, err := ReadPublicKeyFile(publicKeyFile)
			if err != nil {
				fmt.Printf("   ❌ Failed to read public key: %v\n", err)
			} else {
				publicKeys[keyName] = publicKeyPEM
				fmt.Printf("   ✅ Public key read successfully\n")
			}
		}
	}

	fmt.Println()
	fmt.Println("🎯 Results")
	fmt.Println("==========")

	if len(publicKeys) == 0 {
		fmt.Println("❌ No public keys could be extracted or found.")
		fmt.Println("   Make sure the following files exist:")
		for _, file := range keyFiles {
			fmt.Printf("   - %s\n", file)
		}
		os.Exit(1)
	}

	// Generate Go code constants
	fmt.Println("📝 Go Code Constants for Authorized Software:")
	fmt.Println("---------------------------------------------")
	fmt.Println()

	for keyName, publicKeyPEM := range publicKeys {
		constantName := ""
		switch keyName {
		case "Machine ID Decryption":
			constantName = "MACHINE_ID_DECRYPTION_PUBLIC_KEY"
		case "Signature Verification":
			constantName = "SIGNATURE_VERIFICATION_PUBLIC_KEY"
		case "Company ID Decryption":
			constantName = "COMPANY_ID_DECRYPTION_PUBLIC_KEY"
		}

		fmt.Printf("const %s = `%s`\n\n", constantName, publicKeyPEM)
	}

	// Save to file
	outputFile := "public_keys_for_authorized_software.go"
	fmt.Printf("💾 Saving to file: %s\n", outputFile)

	goCode := `package main

// Public keys for V27 license validation
// Generated by extract_public_keys_for_authorized_software.go

`

	for keyName, publicKeyPEM := range publicKeys {
		constantName := ""
		comment := ""
		switch keyName {
		case "Machine ID Decryption":
			constantName = "MACHINE_ID_DECRYPTION_PUBLIC_KEY"
			comment = "// Public key for decrypting machine ID from license"
		case "Signature Verification":
			constantName = "SIGNATURE_VERIFICATION_PUBLIC_KEY"
			comment = "// Public key for verifying license signature"
		case "Company ID Decryption":
			constantName = "COMPANY_ID_DECRYPTION_PUBLIC_KEY"
			comment = "// Public key for decrypting company ID from encrypted_data_block"
		}

		goCode += fmt.Sprintf("%s\nconst %s = `%s`\n\n", comment, constantName, publicKeyPEM)
	}

	err := ioutil.WriteFile(outputFile, []byte(goCode), 0644)
	if err != nil {
		fmt.Printf("❌ Failed to save file: %v\n", err)
	} else {
		fmt.Printf("✅ Public keys saved to %s\n", outputFile)
	}

	fmt.Println()
	fmt.Println("📋 Integration Checklist for Authorized Software:")
	fmt.Println("-------------------------------------------------")
	fmt.Println("1. ✅ Copy the three public key constants to your code")
	fmt.Println("2. ✅ Update license data structure to include 'encrypted_data_block'")
	fmt.Println("3. ✅ Update signature data structure to include 'CompanyIDHash'")
	fmt.Println("4. ✅ Add company ID decryption function")
	fmt.Println("5. ✅ Update signature verification to include company ID hash")
	fmt.Println("6. ✅ Test with V27 license files")
	fmt.Println()
	fmt.Println("📖 See AUTHORIZED_SOFTWARE_INTEGRATION_GUIDE.md for detailed instructions")

	fmt.Println()
	fmt.Println("🔍 Key Summary:")
	fmt.Println("---------------")
	for keyName := range publicKeys {
		fmt.Printf("✅ %s: Ready\n", keyName)
	}

	if len(publicKeys) == 3 {
		fmt.Println()
		fmt.Println("🎉 All three required public keys are available!")
		fmt.Println("   Your authorized software can now validate V27 licenses with company ID support.")
	} else {
		fmt.Println()
		fmt.Printf("⚠️  Only %d out of 3 required keys found. Please ensure all key files are present.\n", len(publicKeys))
	}
}
