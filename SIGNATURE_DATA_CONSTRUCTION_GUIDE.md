# License Signature Data Construction Guide
# 许可证签名数据构建指南

## 📋 Overview / 概述

This document explains how the digital signature is constructed for license files in the license validation system.

本文档解释了许可证验证系统中如何为许可证文件构建数字签名。

## 🔧 Signature Construction Process / 签名构建过程

### Step 1: Create Signature Data Structure / 第1步：创建签名数据结构

The signature is based on a compact data structure designed to fit within RSA-2048 limits:

签名基于一个紧凑的数据结构，设计为适合RSA-2048的限制：

```go
type SignatureData struct {
    CompanyName    string `json:"c"` // Company name (shortened key)
    Email          string `json:"e"` // Email (shortened key)
    Software       string `json:"s"` // Software name (shortened key)
    Version        string `json:"v"` // Software version (shortened key)
    ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
    MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}
```

### Step 2: Populate Signature Data / 第2步：填充签名数据

```go
sigData := SignatureData{
    CompanyName:    licenseData.CompanyName,        // e.g., "gwm2"
    Email:          licenseData.Email,              // e.g., "<EMAIL>"
    Software:       licenseData.AuthorizedSoftware, // e.g., "LS-DYNA Model License Generate Factory"
    Version:        licenseData.AuthorizedVersion,  // e.g., "2.3.0"
    ExpirationUnix: expirationTime.Unix(),          // e.g., ********** (2025-08-10 as Unix timestamp)
    MachineIDHash:  hashString(machineID),          // e.g., "jKl9mN2pQ3rS" (first 16 chars of SHA256 hash)
}
```

### Step 3: Machine ID Hashing / 第3步：机器ID哈希

The machine ID is hashed for compactness and security:

机器ID被哈希以实现紧凑性和安全性：

```go
func hashString(input string) string {
    hash := sha256.Sum256([]byte(input))                    // Create SHA256 hash
    encoded := base64.StdEncoding.EncodeToString(hash[:])   // Base64 encode
    if len(encoded) > 16 {
        return encoded[:16]  // Take first 16 characters for compactness
    }
    return encoded
}
```

### Step 4: JSON Serialization / 第4步：JSON序列化

The signature data is converted to JSON with shortened keys:

签名数据被转换为带有缩短键的JSON：

```json
{
  "c": "gwm2",
  "e": "<EMAIL>", 
  "s": "LS-DYNA Model License Generate Factory",
  "v": "2.3.0",
  "x": **********,
  "m": "jKl9mN2pQ3rS"
}
```

### Step 5: Hash Creation / 第5步：创建哈希

```go
jsonData, _ := json.Marshal(sigData)    // Convert to JSON bytes
hash := sha256.Sum256(jsonData)         // Create SHA256 hash of JSON
```

### Step 6: RSA Signature / 第6步：RSA签名

```go
signature, _ := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
signatureBase64 := base64.StdEncoding.EncodeToString(signature)
```

## 📊 Example Signature Construction / 签名构建示例

### Input Data / 输入数据
```go
License Data:
- CompanyName: "gwm2"
- Email: "<EMAIL>"
- AuthorizedSoftware: "LS-DYNA Model License Generate Factory"
- AuthorizedVersion: "2.3.0"
- ExpirationDate: "2025-08-10"

Machine ID: "711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104"
```

### Step-by-Step Construction / 逐步构建

#### 1. Create SignatureData / 创建SignatureData
```go
sigData := SignatureData{
    CompanyName:    "gwm2",
    Email:          "<EMAIL>",
    Software:       "LS-DYNA Model License Generate Factory",
    Version:        "2.3.0",
    ExpirationUnix: **********,  // 2025-08-10 00:00:00 UTC
    MachineIDHash:  "jKl9mN2pQ3rS",  // First 16 chars of SHA256(machineID)
}
```

#### 2. JSON Serialization / JSON序列化
```json
{"c":"gwm2","e":"<EMAIL>","s":"LS-DYNA Model License Generate Factory","v":"2.3.0","x":**********,"m":"jKl9mN2pQ3rS"}
```

#### 3. SHA256 Hash / SHA256哈希
```
Input: JSON bytes
Output: 32-byte SHA256 hash
Example: a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
```

#### 4. RSA Signature / RSA签名
```
Input: SHA256 hash (32 bytes)
Algorithm: RSA-PKCS1v15 with SHA256
Key: 2048-bit RSA private key
Output: 256-byte signature
```

#### 5. Base64 Encoding / Base64编码
```
Input: 256-byte signature
Output: Base64 string (344 characters)
Example: "h0/mo+4yQupr7QmbZCr7w80c4JwFSMvxFp7FlqsUTUaF0s59PYAw0j63yb26OLUi..."
```

## 🔍 Signature Verification Process / 签名验证过程

The verification process reverses the construction:

验证过程逆转构建过程：

### 1. Parse License Data / 解析许可证数据
```go
var license LicenseData
json.Unmarshal(licenseFileData, &license)
```

### 2. Reconstruct Signature Data / 重构签名数据
```go
expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
decryptedMachineID, _ := decryptMachineID(license.EncryptedMachineID)

sigData := SignatureData{
    CompanyName:    license.CompanyName,
    Email:          license.Email,
    Software:       license.AuthorizedSoftware,
    Version:        license.AuthorizedVersion,
    ExpirationUnix: expirationTime.Unix(),
    MachineIDHash:  hashString(decryptedMachineID),
}
```

### 3. Verify Signature / 验证签名
```go
jsonData, _ := json.Marshal(sigData)
hash := sha256.Sum256(jsonData)
signature, _ := base64.StdEncoding.DecodeString(license.Signature)

err := rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
// err == nil means signature is valid
```

## 🎯 Design Rationale / 设计原理

### 1. Compact Structure / 紧凑结构
- **Shortened JSON keys** (c, e, s, v, x, m) reduce data size
- **Machine ID hashing** reduces 64+ char ID to 16 chars
- **Unix timestamp** more compact than date string

- **缩短的JSON键** (c, e, s, v, x, m) 减少数据大小
- **机器ID哈希** 将64+字符ID减少到16字符
- **Unix时间戳** 比日期字符串更紧凑

### 2. Security Features / 安全特性
- **All critical data included** in signature
- **Machine binding** through hashed machine ID
- **Tamper detection** through digital signature
- **Expiration enforcement** through timestamp

- **所有关键数据** 都包含在签名中
- **机器绑定** 通过哈希机器ID
- **篡改检测** 通过数字签名
- **过期强制** 通过时间戳

### 3. RSA-2048 Compatibility / RSA-2048兼容性
- **Maximum signable data**: ~245 bytes for RSA-2048
- **Typical signature data size**: ~150-200 bytes
- **Safety margin**: Adequate for various license configurations

- **最大可签名数据**: RSA-2048约245字节
- **典型签名数据大小**: 约150-200字节
- **安全边际**: 足以应对各种许可证配置

## 🔒 Security Considerations / 安全考虑

### 1. Data Integrity / 数据完整性
- Any modification to license data will break signature verification
- Machine ID binding prevents license transfer
- Expiration timestamp prevents indefinite use

- 对许可证数据的任何修改都会破坏签名验证
- 机器ID绑定防止许可证转移
- 过期时间戳防止无限期使用

### 2. Attack Resistance / 抗攻击性
- **Signature forgery**: Requires private key (computationally infeasible)
- **License modification**: Detected by signature verification
- **Replay attacks**: Prevented by machine binding and expiration

- **签名伪造**: 需要私钥（计算上不可行）
- **许可证修改**: 通过签名验证检测
- **重放攻击**: 通过机器绑定和过期防止

## 📋 Implementation Checklist / 实现检查清单

For developers implementing signature verification:
对于实现签名验证的开发者：

- [ ] Use exact same SignatureData structure
- [ ] Apply same machine ID hashing (first 16 chars of SHA256)
- [ ] Convert expiration date to Unix timestamp correctly
- [ ] Use same JSON marshaling (Go's default)
- [ ] Apply SHA256 hash to JSON bytes
- [ ] Use RSA-PKCS1v15 with SHA256 for verification
- [ ] Handle Base64 decoding of signature

- [ ] 使用完全相同的SignatureData结构
- [ ] 应用相同的机器ID哈希（SHA256的前16字符）
- [ ] 正确将过期日期转换为Unix时间戳
- [ ] 使用相同的JSON编组（Go的默认值）
- [ ] 对JSON字节应用SHA256哈希
- [ ] 使用RSA-PKCS1v15与SHA256进行验证
- [ ] 处理签名的Base64解码

---

**This signature construction ensures both security and compatibility across the license validation system.**

**此签名构建确保了许可证验证系统的安全性和兼容性。**
