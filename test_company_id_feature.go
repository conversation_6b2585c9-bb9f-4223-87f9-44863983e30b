package main

import (
	"fmt"
	"log"
	"time"
)

func main() {
	fmt.Println("🚀 Testing Company ID Feature")
	fmt.Println("=============================")

	// Test 1: Company Registry Management
	fmt.Println("\n📋 Test 1: Company Registry Management")
	fmt.Println("--------------------------------------")

	// Load or create registry
	registry, err := LoadCompanyRegistry()
	if err != nil {
		log.Fatalf("Failed to load company registry: %v", err)
	}

	fmt.Printf("✅ Registry loaded successfully\n")
	fmt.Printf("   Version: %s\n", registry.Version)
	fmt.Printf("   Next Available ID: %d\n", registry.NextAvailableID)
	fmt.Printf("   Existing Companies: %d\n", len(registry.Companies))

	// Test 2: Company ID Formatting
	fmt.Println("\n🔢 Test 2: Company ID Formatting")
	fmt.Println("---------------------------------")

	testIDs := []int{1000000, 1000001, 1234567, 9999999}
	for _, id := range testIDs {
		formatted := FormatCompanyID(id)
		parsed, err := ParseCompanyID(formatted)
		if err != nil {
			fmt.Printf("❌ Error parsing %s: %v\n", formatted, err)
		} else if parsed != id {
			fmt.Printf("❌ Mismatch: %d -> %s -> %d\n", id, formatted, parsed)
		} else {
			fmt.Printf("✅ %d -> %s -> %d\n", id, formatted, parsed)
		}
	}

	// Test 3: Company ID Validation
	fmt.Println("\n✅ Test 3: Company ID Validation")
	fmt.Println("---------------------------------")

	validIDs := []string{"100-0000", "123-4567", "999-9999"}
	invalidIDs := []string{"12-3456", "1234-567", "abc-defg", "123-456a"}

	for _, id := range validIDs {
		if err := ValidateCompanyID(id, registry); err != nil {
			fmt.Printf("❌ Valid ID rejected: %s - %v\n", id, err)
		} else {
			fmt.Printf("✅ Valid ID accepted: %s\n", id)
		}
	}

	for _, id := range invalidIDs {
		if err := ValidateCompanyID(id, registry); err == nil {
			fmt.Printf("❌ Invalid ID accepted: %s\n", id)
		} else {
			fmt.Printf("✅ Invalid ID rejected: %s - %v\n", id, err)
		}
	}

	// Test 4: Auto Company ID Generation
	fmt.Println("\n🏭 Test 4: Auto Company ID Generation")
	fmt.Println("-------------------------------------")

	testCompanies := []struct {
		name  string
		email string
		phone string
	}{
		{"Test Company A", "<EMAIL>", "18888888888"},
		{"Test Company B", "<EMAIL>", "18888888889"},
		{"Test Company A", "<EMAIL>", "18888888888"}, // Duplicate to test existing lookup
	}

	for i, company := range testCompanies {
		companyID, err := GetOrCreateCompanyID(company.name, company.email, company.phone, registry)
		if err != nil {
			fmt.Printf("❌ Test %d failed: %v\n", i+1, err)
		} else {
			fmt.Printf("✅ Test %d: %s -> %s\n", i+1, company.name, companyID)
		}
	}

	// Test 5: Save Registry
	fmt.Println("\n💾 Test 5: Save Registry")
	fmt.Println("------------------------")

	if err := SaveCompanyRegistry(registry); err != nil {
		fmt.Printf("❌ Failed to save registry: %v\n", err)
	} else {
		fmt.Printf("✅ Registry saved successfully\n")
		fmt.Printf("   Total companies: %d\n", len(registry.Companies))
	}

	// Test 6: Company ID Encryption
	fmt.Println("\n🔐 Test 6: Company ID Encryption")
	fmt.Println("---------------------------------")

	// Load company ID encryption key
	companyIDKey, err := LoadCompanyIDPrivateKey("company_id_encryption_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load company ID key: %v\n", err)
		return
	}

	testCompanyID := "123-4567"
	encryptedID, err := EncryptCompanyID(testCompanyID, companyIDKey)
	if err != nil {
		fmt.Printf("❌ Failed to encrypt company ID: %v\n", err)
	} else {
		fmt.Printf("✅ Company ID encrypted successfully\n")
		fmt.Printf("   Original: %s\n", testCompanyID)
		fmt.Printf("   Encrypted: %s...\n", encryptedID[:50])
	}

	fmt.Println("\n🎉 All tests completed!")
	fmt.Println("========================")
	fmt.Println("✅ Company ID feature is working correctly")
	fmt.Println("📁 Check company_registry.json for the registry file")
	fmt.Println("🔑 Company ID encryption keys are ready")
}
