# Company ID Mapping Documentation
# 公司ID映射文档

## Overview / 概述

This document records the mapping between company names and their unique 7-digit Company IDs. Each company is assigned a unique ID in the format `XXX-XXXX` (e.g., `123-4567`).

本文档记录公司名称与其唯一7位数字公司ID之间的映射关系。每个公司都被分配一个格式为`XXX-XXXX`的唯一ID（例如：`123-4567`）。

## Company ID Format / 公司ID格式

- **Format**: `XXX-XXXX` (3 digits - 4 digits)
- **Range**: `100-0000` to `999-9999`
- **Total Capacity**: 900,000 unique IDs
- **Starting Range**: `100-0000` (1,000,000 in numeric form)

## Automatic Management / 自动管理

The system automatically manages company IDs through the `company_registry.json` file:

系统通过`company_registry.json`文件自动管理公司ID：

### Features / 功能特性

1. **Auto-Generation**: New company IDs are automatically generated starting from `100-0000`
2. **Duplicate Prevention**: System prevents duplicate company IDs
3. **Company Lookup**: Existing companies are automatically recognized by name
4. **Usage Tracking**: Last usage date is tracked for each company
5. **Registry Backup**: All records are stored in JSON format for easy backup

### Registry File Structure / 注册表文件结构

```json
{
  "version": "1.0",
  "last_updated": "2025-07-13T10:30:00Z",
  "next_available_id": 1000001,
  "companies": [
    {
      "company_id": "100-0000",
      "company_name": "Example Company Ltd",
      "email": "<EMAIL>",
      "phone": "18888888888",
      "created_date": "2025-07-13",
      "last_used": "2025-07-13",
      "notes": "First license generated"
    }
  ]
}
```

## Manual Company ID Assignment / 手动公司ID分配

If you need to manually assign a specific company ID:

如果需要手动分配特定的公司ID：

1. Enter the desired 7-digit ID in the GUI (format: `XXX-XXXX`)
2. The system will validate the ID format and check for duplicates
3. If valid and unique, the ID will be assigned to the company
4. The company record will be automatically created in the registry

## Company ID Ranges / 公司ID范围

### Reserved Ranges / 保留范围

- `100-0000` to `199-9999`: General companies (100,000 IDs)
- `200-0000` to `299-9999`: Premium partners (100,000 IDs)
- `300-0000` to `399-9999`: Government/Enterprise (100,000 IDs)
- `400-0000` to `899-9999`: Future expansion (500,000 IDs)
- `900-0000` to `999-9999`: Special/Testing (100,000 IDs)

### Current Usage / 当前使用情况

The system starts assigning IDs from `100-0000` and increments automatically.

系统从`100-0000`开始分配ID并自动递增。

## Security Features / 安全特性

1. **Encryption**: Company IDs are encrypted in the license file using RSA-2048
2. **Signature**: Company ID hash is included in the digital signature
3. **Verification**: License validators can verify company ID authenticity
4. **Key Separation**: Separate RSA key pair for company ID encryption

## Backup and Recovery / 备份与恢复

### Backup / 备份

- Regularly backup `company_registry.json`
- Keep copies of the company ID encryption keys
- Export company records periodically

### Recovery / 恢复

- Restore `company_registry.json` from backup
- Ensure encryption keys are available
- Verify registry integrity after restoration

## Troubleshooting / 故障排除

### Common Issues / 常见问题

1. **Duplicate Company ID**: Check registry for existing assignments
2. **Invalid Format**: Ensure ID follows `XXX-XXXX` format
3. **Registry Corruption**: Restore from backup or recreate
4. **Key Missing**: Regenerate company ID encryption keys if needed

### Support / 支持

For technical support or questions about company ID management, refer to the main documentation or contact the system administrator.

如需技术支持或有关公司ID管理的问题，请参考主要文档或联系系统管理员。

---

**Last Updated**: 2025-07-13  
**Version**: 1.0  
**Maintained By**: License Generator System
