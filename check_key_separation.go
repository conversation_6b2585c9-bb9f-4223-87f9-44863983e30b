// check_key_separation.go
// 独立验证四个密钥文件是否真的是两对不同的密钥

package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"math/big"
	"os"
)

func main() {
	fmt.Println("🔍 Checking Key Separation in Four Key Files")
	fmt.Println("============================================")

	// 检查四个密钥文件是否存在
	keyFiles := []string{
		"machine_id_decryption_private_key.pem",
		"machine_id_decryption_public_key.pem",
		"signature_generation_private_key.pem",
		"signature_verification_public_key.pem",
	}

	fmt.Println("📝 Checking if all key files exist...")
	for _, file := range keyFiles {
		if _, err := os.Stat(file); os.IsNotExist(err) {
			fmt.Printf("❌ File not found: %s\n", file)
			return
		}
		fmt.Printf("✅ Found: %s\n", file)
	}

	// 1. 加载机器ID解密密钥对
	fmt.Println("\n📝 1. Loading Machine ID Decryption Key Pair...")

	machinePrivKey, err := loadPrivateKey("machine_id_decryption_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load machine private key: %v\n", err)
		return
	}

	machinePubKey, err := loadPublicKey("machine_id_decryption_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load machine public key: %v\n", err)
		return
	}

	// 2. 加载签名密钥对
	fmt.Println("📝 2. Loading Signature Key Pair...")

	signaturePrivKey, err := loadPrivateKey("signature_generation_private_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load signature private key: %v\n", err)
		return
	}

	signaturePubKey, err := loadPublicKey("signature_verification_public_key.pem")
	if err != nil {
		fmt.Printf("❌ Failed to load signature public key: %v\n", err)
		return
	}

	fmt.Println("✅ All four key files loaded successfully")

	// 3. 验证机器ID密钥对是否匹配
	fmt.Println("\n🔍 3. Verifying Machine ID Key Pair...")
	machinePrivPubKey := &machinePrivKey.PublicKey

	if comparePublicKeys(machinePubKey, machinePrivPubKey) {
		fmt.Println("✅ Machine ID key pair matches (private ↔ public)")
	} else {
		fmt.Println("❌ Machine ID key pair does NOT match!")
		return
	}

	// 4. 验证签名密钥对是否匹配
	fmt.Println("🔍 4. Verifying Signature Key Pair...")
	signaturePrivPubKey := &signaturePrivKey.PublicKey

	if comparePublicKeys(signaturePubKey, signaturePrivPubKey) {
		fmt.Println("✅ Signature key pair matches (private ↔ public)")
	} else {
		fmt.Println("❌ Signature key pair does NOT match!")
		return
	}

	// 5. 关键检查：验证两对密钥是否不同
	fmt.Println("\n🔍 5. CRITICAL: Verifying Key Pairs Are Different...")

	machineKeysSame := comparePrivateKeys(machinePrivKey, signaturePrivKey)
	publicKeysSame := comparePublicKeys(machinePubKey, signaturePubKey)

	if machineKeysSame {
		fmt.Println("❌ PROBLEM: Machine and Signature private keys are THE SAME!")
		fmt.Println("   机器解密私钥 = 签名生成私钥 (同一密钥)")
	} else {
		fmt.Println("✅ GOOD: Machine and Signature private keys are DIFFERENT")
		fmt.Println("   机器解密私钥 ≠ 签名生成私钥 (不同密钥)")
	}

	if publicKeysSame {
		fmt.Println("❌ PROBLEM: Machine and Signature public keys are THE SAME!")
		fmt.Println("   机器解密公钥 = 签名验证公钥 (同一密钥)")
	} else {
		fmt.Println("✅ GOOD: Machine and Signature public keys are DIFFERENT")
		fmt.Println("   机器解密公钥 ≠ 签名验证公钥 (不同密钥)")
	}

	// 6. 交叉验证 (这是被授权软件检查的关键点)
	fmt.Println("\n🧪 6. Cross-Validation Test (What Authorized Software Checks):")
	fmt.Println("==============================================================")

	// 检查机器私钥是否与签名公钥配对
	crossMatch1 := comparePublicKeys(signaturePubKey, machinePrivPubKey)
	if crossMatch1 {
		fmt.Println("❌ CRITICAL: Machine private key matches signature public key!")
		fmt.Println("   机器解密私钥 ↔ 签名验证公钥 是同一密钥对")
		fmt.Println("   这就是被授权软件反馈的问题!")
	} else {
		fmt.Println("✅ GOOD: Machine private key does NOT match signature public key")
		fmt.Println("   机器解密私钥 ↔ 签名验证公钥 是不同密钥对")
	}

	// 检查签名私钥是否与机器公钥配对
	crossMatch2 := comparePublicKeys(machinePubKey, signaturePrivPubKey)
	if crossMatch2 {
		fmt.Println("❌ CRITICAL: Signature private key matches machine public key!")
		fmt.Println("   签名生成私钥 ↔ 机器解密公钥 是同一密钥对")
	} else {
		fmt.Println("✅ GOOD: Signature private key does NOT match machine public key")
		fmt.Println("   签名生成私钥 ↔ 机器解密公钥 是不同密钥对")
	}

	// 7. 显示密钥指纹
	fmt.Println("\n📋 Key Fingerprints:")
	fmt.Println("====================")

	fmt.Printf("🔑 Machine Private Key:    %s\n", getKeyFingerprint(machinePrivKey.N))
	fmt.Printf("🔓 Machine Public Key:     %s\n", getKeyFingerprint(machinePubKey.N))
	fmt.Printf("✍️  Signature Private Key:  %s\n", getKeyFingerprint(signaturePrivKey.N))
	fmt.Printf("🔍 Signature Public Key:   %s\n", getKeyFingerprint(signaturePubKey.N))

	// 8. 最终结论
	fmt.Println("\n🎯 Final Analysis:")
	fmt.Println("==================")

	if machineKeysSame || publicKeysSame || crossMatch1 || crossMatch2 {
		fmt.Println("❌ PROBLEM CONFIRMED: The four keys are NOT two separate key pairs!")
		fmt.Println("   确认问题: 四个密钥文件不是两对独立的密钥对!")
		fmt.Println()
		fmt.Println("   被授权软件的反馈是正确的:")
		fmt.Println("   - 这些密钥文件不是真正分离的两对密钥")
		fmt.Println("   - 需要重新生成真正独立的密钥对")

		if crossMatch1 {
			fmt.Println("   - 特别是: 机器解密私钥与签名验证公钥是同一密钥对")
		}
	} else {
		fmt.Println("✅ SUCCESS: The four keys are truly two separate key pairs!")
		fmt.Println("   成功: 四个密钥文件是真正分离的两对密钥对!")
		fmt.Println()
		fmt.Println("   密钥对1 (机器绑定): machine_id_decryption_private_key.pem ↔ machine_id_decryption_public_key.pem")
		fmt.Println("   密钥对2 (数字签名): signature_generation_private_key.pem ↔ signature_verification_public_key.pem")
	}
}

func loadPrivateKey(filename string) (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}

func loadPublicKey(filename string) (*rsa.PublicKey, error) {
	keyData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return publicKey, nil
}

func comparePrivateKeys(key1, key2 *rsa.PrivateKey) bool {
	return key1.N.Cmp(key2.N) == 0 && key1.E == key2.E
}

func comparePublicKeys(key1, key2 *rsa.PublicKey) bool {
	return key1.N.Cmp(key2.N) == 0 && key1.E == key2.E
}

func getKeyFingerprint(n *big.Int) string {
	nBytes := n.Bytes()
	if len(nBytes) >= 8 {
		return fmt.Sprintf("%x...", nBytes[:8])
	}
	return fmt.Sprintf("%x", nBytes)
}
