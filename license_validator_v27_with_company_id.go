package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"
	"time"
)

// LicenseData represents the V27 license structure with company ID
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedDataBlock string `json:"encrypted_data_block"` // Encrypted Company ID
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used for signature verification (must match generator)
type SignatureData struct {
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	StartUnix      int64  `json:"b"` // Start date as Unix timestamp
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp
	MachineIDHash  string `json:"m"` // Hash of machine ID
	CompanyIDHash  string `json:"c"` // Hash of company ID (NEW in V27)
}

// LicenseValidationResult contains validation results
type LicenseValidationResult struct {
	Valid     bool
	CompanyID string        // Decrypted company ID
	MachineID string        // Decrypted machine ID
	License   *LicenseData
	Error     string
}

// Embed the three required public keys (replace with your actual keys)
const MACHINE_ID_DECRYPTION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890...
-----END PUBLIC KEY-----`

const SIGNATURE_VERIFICATION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0987654321...
-----END PUBLIC KEY-----`

const COMPANY_ID_DECRYPTION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5555555555...
-----END PUBLIC KEY-----`

// LoadPublicKeyFromString loads RSA public key from PEM string
func LoadPublicKeyFromString(pemString string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(pemString))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return rsaPub, nil
}

// LoadLicense loads license from JSON file
func LoadLicense(filename string) (*LicenseData, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read license file: %v", err)
	}

	var license LicenseData
	if err := json.Unmarshal(data, &license); err != nil {
		return nil, fmt.Errorf("failed to parse license JSON: %v", err)
	}

	return &license, nil
}

// DecryptData decrypts base64 encoded data using RSA public key
func DecryptData(encryptedData string, publicKey *rsa.PublicKey) (string, error) {
	// Decode base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	// Decrypt using RSA
	decrypted, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, publicKey, data, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt data: %v", err)
	}

	return string(decrypted), nil
}

// hashString creates SHA256 hash of input string
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// VerifyLicenseSignature verifies the license signature including company ID
func VerifyLicenseSignature(license *LicenseData, machineID, companyID string, publicKey *rsa.PublicKey) error {
	// Parse dates
	startTime, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		return fmt.Errorf("failed to parse start date: %v", err)
	}

	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}

	// Create signature data (must match generator format exactly)
	sigData := SignatureData{
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(machineID),
		CompanyIDHash:  hashString(companyID), // NEW: Include company ID hash
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// Create hash
	hash := sha256.Sum256(jsonData)

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// Verify signature
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

// GetCurrentMachineID gets the current machine ID (implement according to your system)
func GetCurrentMachineID() string {
	// This is a placeholder - implement your actual machine ID logic
	// Should match the machine ID used in license generation
	return "your-actual-machine-id"
}

// ValidateLicense performs complete license validation including company ID
func ValidateLicense(licenseFile string) (*LicenseValidationResult, error) {
	result := &LicenseValidationResult{}

	// 1. Load and parse license
	license, err := LoadLicense(licenseFile)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to load license: %v", err)
		return result, err
	}
	result.License = license

	// 2. Check if this is V27 format (has encrypted_data_block)
	if license.EncryptedDataBlock == "" {
		result.Error = "This validator only supports V27 license format with company ID"
		return result, fmt.Errorf(result.Error)
	}

	// 3. Decrypt machine ID
	machineIDKey, err := LoadPublicKeyFromString(MACHINE_ID_DECRYPTION_PUBLIC_KEY)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to load machine ID key: %v", err)
		return result, err
	}

	machineID, err := DecryptData(license.EncryptedMachineID, machineIDKey)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to decrypt machine ID: %v", err)
		return result, err
	}
	result.MachineID = machineID

	// 4. Decrypt company ID (NEW in V27)
	companyIDKey, err := LoadPublicKeyFromString(COMPANY_ID_DECRYPTION_PUBLIC_KEY)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to load company ID key: %v", err)
		return result, err
	}

	companyID, err := DecryptData(license.EncryptedDataBlock, companyIDKey)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to decrypt company ID: %v", err)
		return result, err
	}
	result.CompanyID = companyID

	// 5. Verify signature (including company ID)
	signatureKey, err := LoadPublicKeyFromString(SIGNATURE_VERIFICATION_PUBLIC_KEY)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to load signature key: %v", err)
		return result, err
	}

	err = VerifyLicenseSignature(license, machineID, companyID, signatureKey)
	if err != nil {
		result.Error = fmt.Sprintf("Signature verification failed: %v", err)
		return result, err
	}

	// 6. Validate machine binding
	currentMachineID := GetCurrentMachineID()
	if machineID != currentMachineID {
		result.Error = "License is not valid for this machine"
		return result, fmt.Errorf(result.Error)
	}

	// 7. Check expiration
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to parse expiration date: %v", err)
		return result, err
	}

	if time.Now().After(expirationTime) {
		result.Error = "License has expired"
		return result, fmt.Errorf(result.Error)
	}

	// 8. Check start date
	startTime, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to parse start date: %v", err)
		return result, err
	}

	if time.Now().Before(startTime) {
		result.Error = "License is not yet active"
		return result, fmt.Errorf(result.Error)
	}

	// All validations passed
	result.Valid = true
	return result, nil
}

// EnableCompanyFeatures enables features based on company ID
func EnableCompanyFeatures(companyID string) {
	fmt.Printf("Enabling features for company ID: %s\n", companyID)

	switch {
	case strings.HasPrefix(companyID, "1"):
		fmt.Println("✅ Standard features enabled (General companies)")
	case strings.HasPrefix(companyID, "2"):
		fmt.Println("✅ Premium features enabled (Premium partners)")
	case strings.HasPrefix(companyID, "3"):
		fmt.Println("✅ Enterprise features enabled (Government/Enterprise)")
	case strings.HasPrefix(companyID, "9"):
		fmt.Println("✅ Testing features enabled (Special/Testing)")
	default:
		fmt.Println("✅ Basic features enabled")
	}
}

func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: license_validator_v27 <license_file>")
	}

	licenseFile := os.Args[1]

	fmt.Println("🔍 V27 License Validator with Company ID Support")
	fmt.Println("================================================")
	fmt.Printf("Validating license: %s\n\n", licenseFile)

	// Validate license
	result, err := ValidateLicense(licenseFile)
	if err != nil {
		fmt.Printf("❌ License validation failed: %s\n", result.Error)
		os.Exit(1)
	}

	// Display results
	fmt.Println("✅ License validation successful!")
	fmt.Println("\n📋 License Information:")
	fmt.Printf("   Company Name: %s\n", result.License.CompanyName)
	fmt.Printf("   Company ID: %s\n", result.CompanyID)
	fmt.Printf("   Email: %s\n", result.License.Email)
	fmt.Printf("   Phone: %s\n", result.License.Phone)
	fmt.Printf("   Software: %s\n", result.License.AuthorizedSoftware)
	fmt.Printf("   Version: %s\n", result.License.AuthorizedVersion)
	fmt.Printf("   License Type: %s\n", result.License.LicenseType)
	fmt.Printf("   Start Date: %s\n", result.License.StartDate)
	fmt.Printf("   Expiration: %s\n", result.License.ExpirationDate)
	fmt.Printf("   Machine ID: %s\n", result.MachineID)

	// Enable company-specific features
	fmt.Println("\n🎯 Company-Specific Features:")
	EnableCompanyFeatures(result.CompanyID)

	fmt.Println("\n🚀 Your software is now authorized to run!")
}
