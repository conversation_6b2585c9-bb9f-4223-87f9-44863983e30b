# Quick Migration Guide for Authorized Software
# 被授权软件快速迁移指南

## 🚀 5-Minute Upgrade / 5分钟升级

### Step 1: Backup Current Validator / 第1步：备份当前验证器
```bash
cp your_license_validator.go your_license_validator_backup.go
```

### Step 2: Replace with Enhanced Validator / 第2步：替换为增强验证器
```bash
cp ENHANCED_LICENSE_VALIDATOR_V23.go your_license_validator.go
```

### Step 3: Update Package Name / 第3步：更新包名
```go
// Change the first line from:
package main

// To your package name:
package yourpackage
```

### Step 4: Update Function Call / 第4步：更新函数调用
```go
// Old way (still works)
err := ValidateLicenseFile("license.json")

// New way (recommended)
err := ValidateEnhancedLicenseFile("license.json")
```

### Step 5: Test / 第5步：测试
```go
// Test with your existing license file
err := ValidateEnhancedLicenseFile("your_existing_license.json")
if err != nil {
    log.Printf("Validation failed: %v", err)
} else {
    log.Println("✅ Upgrade successful - existing license works!")
}
```

## 🔍 What Changed / 变化内容

### New License Fields / 新许可证字段

**V22 Added / V22新增:**
```json
{
  "license_type": "lease"  // Values: "lease", "demo", "perpetual"
}
```

**V23 Added / V23新增:**
```json
{
  "license_type": "lease",
  "start_date": "2025-07-12"  // Format: YYYY-MM-DD
}
```

### Automatic Version Detection / 自动版本检测

The enhanced validator automatically detects which version of license you're using:

增强验证器自动检测您使用的许可证版本：

```go
// No configuration needed - automatic detection
validator.DetectLicenseVersion(license)
// Returns: LicenseV1, LicenseV22, or LicenseV23
```

## ✅ Compatibility Guarantee / 兼容性保证

### What Works Without Changes / 无需更改即可工作的内容

- ✅ **All existing license files** / 所有现有许可证文件
- ✅ **Current validation logic** / 当前验证逻辑
- ✅ **Machine binding** / 机器绑定
- ✅ **Expiration checking** / 过期检查
- ✅ **Signature verification** / 签名验证

### What Gets Enhanced / 增强的内容

- 🆕 **License Type validation** / 许可证类型验证
- 🆕 **Start Date validation** / 开始日期验证
- 🆕 **Better error messages** / 更好的错误消息
- 🆕 **Version-aware processing** / 版本感知处理

## 📋 Integration Examples / 集成示例

### Basic Integration / 基础集成

```go
package main

import (
    "log"
    // ... other imports
)

func main() {
    // Validate license at startup
    err := ValidateEnhancedLicenseFile("license.json")
    if err != nil {
        log.Fatal("❌ License validation failed:", err)
    }
    
    log.Println("✅ License validated - starting application")
    
    // Your application logic here
    startApplication()
}
```

### Advanced Integration with Details / 高级集成（含详细信息）

```go
func validateLicenseWithDetails() error {
    validator, err := NewEnhancedLicenseValidator()
    if err != nil {
        return err
    }
    
    license, err := LoadEnhancedLicenseFromFile("license.json")
    if err != nil {
        return err
    }
    
    // Show license information
    version := validator.DetectLicenseVersion(license)
    log.Printf("📋 License Version: V%d", int(version))
    log.Printf("📋 Company: %s", license.CompanyName)
    log.Printf("📋 Software: %s v%s", license.AuthorizedSoftware, license.AuthorizedVersion)
    
    if license.LicenseType != nil {
        log.Printf("📋 Type: %s", *license.LicenseType)
    }
    
    if license.StartDate != nil {
        log.Printf("📋 Active from: %s", *license.StartDate)
    }
    
    log.Printf("📋 Expires: %s", license.ExpirationDate)
    
    // Validate
    return validator.ValidateEnhancedLicense(license)
}
```

### Periodic Validation / 定期验证

```go
func startPeriodicValidation() {
    ticker := time.NewTicker(1 * time.Hour)
    go func() {
        for range ticker.C {
            err := ValidateEnhancedLicenseFile("license.json")
            if err != nil {
                log.Printf("⚠️ License validation failed: %v", err)
                // Handle license failure (e.g., graceful shutdown)
            }
        }
    }()
}
```

## 🔧 Troubleshooting / 故障排除

### Common Issues / 常见问题

**Issue: "failed to parse license JSON"**
```
Solution: Check if license file is valid JSON
Command: cat license.json | jq .
```

**Issue: "license validation failed"**
```
Solution: Check license expiration and machine binding
Debug: Enable detailed logging to see specific failure
```

**Issue: "signature verification failed"**
```
Solution: Ensure you're using the correct embedded keys
Check: Verify the license file hasn't been modified
```

### Debug Mode / 调试模式

```go
func debugLicenseValidation() {
    validator, _ := NewEnhancedLicenseValidator()
    license, _ := LoadEnhancedLicenseFromFile("license.json")
    
    // Check version
    version := validator.DetectLicenseVersion(license)
    fmt.Printf("🔍 Detected version: V%d\n", int(version))
    
    // Check each validation step
    fmt.Println("🔍 Validating basic fields...")
    err := validator.validateBasicFields(license)
    if err != nil {
        fmt.Printf("❌ Basic validation failed: %v\n", err)
        return
    }
    
    fmt.Println("🔍 Validating machine binding...")
    err = validator.validateMachineBinding(license.EncryptedMachineID)
    if err != nil {
        fmt.Printf("❌ Machine binding failed: %v\n", err)
        return
    }
    
    fmt.Println("🔍 Validating signature...")
    err = validator.validateVersionAwareSignature(license, version)
    if err != nil {
        fmt.Printf("❌ Signature validation failed: %v\n", err)
        return
    }
    
    fmt.Println("✅ All validations passed!")
}
```

## 📞 Support / 支持

### If You Need Help / 如果需要帮助

**Before Contacting Support / 联系支持前:**
1. Test with existing license files
2. Check error messages carefully
3. Verify file paths and permissions
4. Try the debug mode above

**Contact Information / 联系信息:**
- Technical Support: [Your support email]
- Documentation: [Your documentation link]
- Emergency: [Emergency contact]

**Include in Support Request / 支持请求中包含:**
- Error messages (full text)
- License file version (V1/V22/V23)
- Operating system and architecture
- Steps to reproduce the issue

## 🎯 Next Steps / 后续步骤

### After Successful Migration / 成功迁移后

1. **Monitor**: Watch for any validation errors in logs
2. **Test**: Verify all license-dependent features work
3. **Update**: Consider updating to new license formats for enhanced features
4. **Document**: Update your internal documentation

### Future Enhancements / 未来增强

With the enhanced validator, you're ready for:
- **License Type Controls**: Different behavior for lease/demo/perpetual
- **Start Date Scheduling**: Delayed license activation
- **Enhanced Reporting**: Better license usage analytics
- **Future Features**: Ready for upcoming license enhancements

---

**🎉 Congratulations!** You've successfully upgraded to the enhanced license validator with full backward compatibility.

**🎉 恭喜！** 您已成功升级到具有完全向后兼容性的增强许可证验证器。
