package main

import (
	"time"
)

// MachineInfo represents the structure of the factory machine info JSON file
type MachineInfo struct {
	CompanyName   string `json:"CompanyName"`
	Email         string `json:"Email"`
	GeneratedBy   string `json:"GeneratedBy"`
	GeneratedDate string `json:"GeneratedDate"`
	MachineID     string `json:"MachineID"` // This is encrypted and needs to be decrypted
	Notes         string `json:"Notes"`
	Phone         string `json:"Phone"`
	Version       string `json:"Version,omitempty"` // Optional version field
}

// LicenseData represents the license information to be generated
// V27+ format: No backward compatibility with older license formats
type LicenseData struct {
	// License format version (V27+ only, no backward compatibility)
	LicenseVersion string `json:"license_version"` // Always "V27" for this version

	// Public information (plaintext for transparency)
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"` // Software name only
	AuthorizedVersion  string `json:"authorized_version"`  // Software version only
	LicenseType        string `json:"license_type"`        // License type: lease, demo, perpetual
	StartDate          string `json:"start_date"`          // License start date in YYYY-MM-DD format
	ExpirationDate     string `json:"expiration_date"`     // Only date in YYYY-MM-DD format
	IssuedDate         string `json:"issued_date"`         // Only date in YYYY-MM-DD format (renamed from generated_date)

	// Company ID binding (for company identification and verification)
	// Note: Only encrypted company ID is stored, no plaintext company ID in license file
	EncryptedCompanyID string `json:"encrypted_company_id"` // Encrypted company ID for security

	// Machine binding (encrypted MachineID from machine info file)
	// Users can compare this with their machine info file to verify it's for their computer
	EncryptedMachineID string `json:"encrypted_machine_id"` // Direct copy from machine info file

	// Digital signature (for integrity and authenticity)
	Signature string `json:"signature"`
}

// SignatureData represents the data used to create the signature
// This should be compact to fit within RSA2048 size limits
// Note: Company name, email, and phone are NOT included in signature verification
type SignatureData struct {
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	StartUnix      int64  `json:"b"` // Start date as Unix timestamp (shortened key: "b" for begin)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	CompanyIDHash  string `json:"c"` // Hash of company ID (shortened key)
}

// AppConfig holds the application configuration
type AppConfig struct {
	MachineInfoPath string
	PrivateKeyPath  string
	LastOutputPath  string
}

// ExpirationPreset represents predefined expiration options
type ExpirationPreset struct {
	Name     string
	Duration time.Duration
	Extra    time.Duration // Additional days (e.g., +2 days)
}

// CompanyRegistry represents the company ID registry for managing company records
type CompanyRegistry struct {
	Version         string          `json:"version"`
	LastUpdated     string          `json:"last_updated"`
	NextAvailableID int             `json:"next_available_id"`
	Companies       []CompanyRecord `json:"companies"`
}

// CompanyRecord represents a single company record in the registry
type CompanyRecord struct {
	CompanyID   string `json:"company_id"`   // Formatted company ID (e.g., "123-4567")
	CompanyName string `json:"company_name"` // Company name
	Email       string `json:"email"`        // Company email
	Phone       string `json:"phone"`        // Company phone
	CreatedDate string `json:"created_date"` // Date when record was created (YYYY-MM-DD)
	LastUsed    string `json:"last_used"`    // Date when last license was generated (YYYY-MM-DD)
	Notes       string `json:"notes"`        // Optional notes
}

// GetExpirationPresets returns the predefined expiration options
func GetExpirationPresets() []ExpirationPreset {
	return []ExpirationPreset{
		{
			Name:     "1 Month + 2 Days",
			Duration: 30 * 24 * time.Hour,
			Extra:    2 * 24 * time.Hour,
		},
		{
			Name:     "3 Months + 2 Days",
			Duration: 90 * 24 * time.Hour,
			Extra:    2 * 24 * time.Hour,
		},
		{
			Name:     "1 Year + 2 Days",
			Duration: 365 * 24 * time.Hour,
			Extra:    2 * 24 * time.Hour,
		},
	}
}
