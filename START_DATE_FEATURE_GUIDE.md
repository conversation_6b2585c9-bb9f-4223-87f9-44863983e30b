# Start Date Feature Guide
# 开始日期功能指南

## 🎯 Overview / 概述

The License Generator V23 now includes a comprehensive Start Date feature that allows users to set when a license becomes active. This feature provides flexible scheduling options and intelligent date validation to ensure license validity.

许可证生成器V23现在包含了全面的开始日期功能，允许用户设置许可证何时生效。此功能提供灵活的调度选项和智能日期验证，以确保许可证的有效性。

## 📅 Start Date Options / 开始日期选项

### Preset Options / 预设选项

#### 1. **📅 Today (Current Date)** - Default / 默认
- **Purpose**: License becomes active immediately
- **Use Case**: Standard immediate activation
- **Date**: Current system date

#### 1. **📅 今天（当前日期）** - 默认
- **用途**: 许可证立即生效
- **使用场景**: 标准即时激活
- **日期**: 当前系统日期

#### 2. **📅 Tomorrow**
- **Purpose**: License becomes active the next day
- **Use Case**: Scheduled activation for next business day
- **Date**: Current date + 1 day

#### 2. **📅 明天**
- **用途**: 许可证次日生效
- **使用场景**: 下一个工作日的计划激活
- **日期**: 当前日期 + 1天

#### 3. **📅 Next Week**
- **Purpose**: License becomes active in one week
- **Use Case**: Planned deployment, scheduled rollout
- **Date**: Current date + 7 days

#### 3. **📅 下周**
- **用途**: 许可证一周后生效
- **使用场景**: 计划部署、预定推出
- **日期**: 当前日期 + 7天

#### 4. **📅 Next Month**
- **Purpose**: License becomes active in one month
- **Use Case**: Future project start, contract beginning
- **Date**: Current date + 1 month

#### 4. **📅 下个月**
- **用途**: 许可证一个月后生效
- **使用场景**: 未来项目开始、合同开始
- **日期**: 当前日期 + 1个月

#### 5. **🗓️ Custom Start Date**
- **Purpose**: User-defined specific start date
- **Use Case**: Precise scheduling, contract-specific dates
- **Interface**: Year/Month/Day dropdown selectors

#### 5. **🗓️ 自定义开始日期**
- **用途**: 用户定义的特定开始日期
- **使用场景**: 精确调度、合同特定日期
- **界面**: 年/月/日下拉选择器

## 🖥️ User Interface Design / 用户界面设计

### Start Date Selection Panel / 开始日期选择面板

```
📅 Start Date
┌─────────────────────────────────────────────────────────┐
│ Select when the license becomes active:                │
│                                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 📅 Today (Current Date)                    ▼       │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ ─────────────────────────────────────────────────────── │
│                                                         │
│ Start Date: 2025-07-12                                 │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### Custom Date Selection / 自定义日期选择

```
🗓️ Custom Start Date
┌─────────────────────────────────────────────────────────┐
│ Year        Month       Day                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                     │
│ │ 2025  ▼ │ │ 07    ▼ │ │ 12    ▼ │                     │
│ └─────────┘ └─────────┘ └─────────┘                     │
│                                                         │
│ Start Date: 2025-07-12                                 │
└─────────────────────────────────────────────────────────┘
```

## 🔧 Technical Implementation / 技术实现

### Data Structure Updates / 数据结构更新

#### LicenseData Structure / 许可证数据结构
```go
type LicenseData struct {
    CompanyName        string `json:"company_name"`
    Email              string `json:"email"`
    Phone              string `json:"phone"`
    AuthorizedSoftware string `json:"authorized_software"`
    AuthorizedVersion  string `json:"authorized_version"`
    LicenseType        string `json:"license_type"`
    StartDate          string `json:"start_date"`          // NEW FIELD
    ExpirationDate     string `json:"expiration_date"`
    IssuedDate         string `json:"issued_date"`
    EncryptedMachineID string `json:"encrypted_machine_id"`
    Signature          string `json:"signature"`
}
```

#### SignatureData Structure / 签名数据结构
```go
type SignatureData struct {
    CompanyName    string `json:"c"`
    Email          string `json:"e"`
    Software       string `json:"s"`
    Version        string `json:"v"`
    LicenseType    string `json:"t"`
    StartUnix      int64  `json:"b"`  // NEW FIELD (b for begin)
    ExpirationUnix int64  `json:"x"`
    MachineIDHash  string `json:"m"`
}
```

### Start Date Calculation Logic / 开始日期计算逻辑

```go
// Calculate start date based on user selection
switch selectedStartPreset {
case "📅 Today (Current Date)":
    startDate = time.Now()
case "📅 Tomorrow":
    startDate = time.Now().AddDate(0, 0, 1)
case "📅 Next Week":
    startDate = time.Now().AddDate(0, 0, 7)
case "📅 Next Month":
    startDate = time.Now().AddDate(0, 1, 0)
case "🗓️ Custom Start Date":
    // Parse from dropdown selections
    startDate = parseCustomDate(year, month, day)
}
```

## ✅ Date Validation System / 日期验证系统

### Intelligent Validation / 智能验证

#### 1. **Future Date Validation / 未来日期验证**
```
✅ Valid: Expiration date is in the future
❌ Invalid: Expiration date is in the past
```

#### 2. **Date Range Validation / 日期范围验证**
```
✅ Valid: Expiration date > Start date
❌ Invalid: Expiration date ≤ Start date
```

#### 3. **User-Friendly Error Messages / 用户友好的错误消息**

```
⚠️ Date Validation Error

Expiration date (2025-07-10) must be later than start date (2025-07-12).

Please adjust your dates to ensure the license has a valid duration.
```

### Validation Logic Flow / 验证逻辑流程

```
1. Calculate Start Date
   ↓
2. Calculate Expiration Date
   ↓
3. Check: Expiration > Current Time
   ↓
4. Check: Expiration > Start Date
   ↓
5. Calculate License Duration
   ↓
6. Generate License
```

## 📋 Generated License Format / 生成的许可证格式

### JSON Output Example / JSON输出示例

```json
{
  "company_name": "Example Company",
  "email": "<EMAIL>",
  "phone": "************",
  "authorized_software": "LS-DYNA Model License Generate Factory",
  "authorized_version": "2.3.0",
  "license_type": "lease",
  "start_date": "2025-07-12",           ← NEW FIELD
  "expiration_date": "2026-01-10",
  "issued_date": "2025-07-11",
  "encrypted_machine_id": "Base64EncodedMachineID...",
  "signature": "Base64EncodedSignature..."
}
```

### Signature Data Example / 签名数据示例

```json
{
  "c": "Example Company",
  "e": "<EMAIL>",
  "s": "LS-DYNA Model License Generate Factory",
  "v": "2.3.0",
  "t": "lease",
  "b": **********,                      ← NEW FIELD (Start date Unix timestamp)
  "x": **********,                      ← Expiration date Unix timestamp
  "m": "HashedMachineID"
}
```

## 🎯 Usage Workflow / 使用工作流

### Enhanced License Generation Process / 增强的许可证生成流程

#### 1. **Configure Files / 配置文件**
- Load machine info JSON file
- Load private key PEM file

#### 2. **Fill License Details / 填写许可证详情**
- Company information
- Software details
- License type selection

#### 3. **Set Start Date / 设置开始日期** 🆕
- Choose from preset options or custom date
- Real-time preview of selected date
- Default: Today (Current Date)

#### 4. **Set Expiration Date / 设置过期日期**
- Choose from preset options or custom date
- Automatic validation against start date

#### 5. **Generate License / 生成许可证**
- Intelligent date validation
- Duration calculation and logging
- Enhanced success dialog with date range

### 分步流程

#### 1. **配置文件**
- 加载机器信息JSON文件
- 加载私钥PEM文件

#### 2. **填写许可证详情**
- 公司信息
- 软件详情
- 许可证类型选择

#### 3. **设置开始日期** 🆕
- 从预设选项或自定义日期中选择
- 所选日期的实时预览
- 默认：今天（当前日期）

#### 4. **设置过期日期**
- 从预设选项或自定义日期中选择
- 自动验证与开始日期的关系

#### 5. **生成许可证**
- 智能日期验证
- 持续时间计算和记录
- 带有日期范围的增强成功对话框

## ✨ Enhanced Success Dialog / 增强的成功对话框

### Comprehensive Information Display / 全面信息显示

```
License generated and saved successfully!

📁 File: factory_license.json
🗂️ Folder opened automatically

Company: Example Company
Software: LS-DYNA Model License Generate Factory v2.3.0
🏷️ Type: lease
📅 Start: 2025-07-12                  ← NEW FIELD
⏰ Expires: 2026-01-10

Duration: 182 days
```

## 🔒 Security Enhancements / 安全增强

### Start Date in Digital Signature / 开始日期在数字签名中

#### Security Benefits / 安全优势
- **Tamper Detection**: Start date modification invalidates signature
- **Complete Validation**: Both start and end dates are cryptographically protected
- **Timeline Integrity**: Ensures license timeline cannot be altered

#### 安全优势
- **篡改检测**: 修改开始日期会使签名无效
- **完整验证**: 开始和结束日期都受到加密保护
- **时间线完整性**: 确保许可证时间线无法被更改

### Signature Data Compactness / 签名数据紧凑性

```go
// Compact signature format for RSA2048 compatibility
type SignatureData struct {
    // ... other fields
    StartUnix      int64  `json:"b"`  // "b" for begin (4 bytes saved vs "start_unix")
    ExpirationUnix int64  `json:"x"`  // "x" for expiration (existing)
    // ... other fields
}
```

## 🚀 Version Information / 版本信息

### V23 Features / V23版本特性

- ✅ **Start Date Selection** / 开始日期选择
- ✅ **Preset Date Options** / 预设日期选项
- ✅ **Custom Date Picker** / 自定义日期选择器
- ✅ **Real-time Date Preview** / 实时日期预览
- ✅ **Intelligent Date Validation** / 智能日期验证
- ✅ **User-Friendly Error Messages** / 用户友好的错误消息
- ✅ **Enhanced Success Dialog** / 增强的成功对话框
- ✅ **Signature Integration** / 签名集成
- ✅ **Duration Calculation** / 持续时间计算

### Backward Compatibility / 向后兼容性

- **Existing Licenses**: Old licenses without start_date field remain valid
- **Signature Verification**: New signature format includes start date
- **Migration Path**: Existing systems can be updated to recognize start dates

### 向后兼容性

- **现有许可证**: 没有start_date字段的旧许可证仍然有效
- **签名验证**: 新签名格式包含开始日期
- **迁移路径**: 现有系统可以更新以识别开始日期

## 📋 Best Practices / 最佳实践

### Start Date Selection Guidelines / 开始日期选择指南

#### When to Use "Today" / 何时使用"今天"
- Immediate license activation
- Standard deployment scenarios
- Real-time software delivery

#### When to Use "Tomorrow" / 何时使用"明天"
- Next business day activation
- Scheduled maintenance windows
- Coordinated team deployments

#### When to Use "Next Week/Month" / 何时使用"下周/下个月"
- Future project phases
- Contract start dates
- Planned software rollouts

#### When to Use "Custom Date" / 何时使用"自定义日期"
- Specific contract requirements
- Precise scheduling needs
- Compliance-driven timelines

### 开始日期选择指南

#### 何时使用"今天"
- 立即激活许可证
- 标准部署场景
- 实时软件交付

#### 何时使用"明天"
- 下一个工作日激活
- 计划维护窗口
- 协调团队部署

#### 何时使用"下周/下个月"
- 未来项目阶段
- 合同开始日期
- 计划软件推出

#### 何时使用"自定义日期"
- 特定合同要求
- 精确调度需求
- 合规驱动的时间线

---

**Note**: The Start Date feature provides comprehensive license scheduling capabilities while maintaining full security and user-friendliness.

**注意**: 开始日期功能提供全面的许可证调度能力，同时保持完全的安全性和用户友好性。
