// test_v23_license_generation.go
// 测试V23许可证生成，验证是否包含新字段

package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
)

func testV23LicenseGeneration() {
	fmt.Println("🧪 Testing V23 License Generation...")
	fmt.Println("=====================================")

	// 创建license生成器 - 使用分离的密钥
	generator, err := NewLicenseGenerator(
		"machine_info.json",               // 机器信息文件
		"private_key.pem",                 // 机器解密密钥（保持不变）
		"license_signing_private_key.pem", // 新的签名密钥
	)
	if err != nil {
		log.Fatalf("Failed to create license generator: %v", err)
	}

	// 设置测试参数
	companyName := "Test Company V23"
	email := "<EMAIL>"
	phone := "************"
	software := "Test Software"
	version := "1.0.0"
	licenseType := "lease"

	// 设置开始日期为今天
	startDate := time.Now()

	// 设置过期日期为一年后
	expirationDate := time.Now().AddDate(1, 0, 0)

	fmt.Printf("📋 Test Parameters:\n")
	fmt.Printf("  Company: %s\n", companyName)
	fmt.Printf("  Software: %s v%s\n", software, version)
	fmt.Printf("  License Type: %s\n", licenseType)
	fmt.Printf("  Start Date: %s\n", startDate.Format("2006-01-02"))
	fmt.Printf("  Expiration Date: %s\n", expirationDate.Format("2006-01-02"))
	fmt.Println()

	// 生成license
	fmt.Println("🔄 Generating V23 license...")
	license, err := generator.GenerateLicense(
		companyName,
		email,
		phone,
		software,
		version,
		licenseType,
		startDate,
		expirationDate,
	)
	if err != nil {
		log.Fatalf("Failed to generate license: %v", err)
	}

	// 验证license包含新字段
	fmt.Println("🔍 Verifying V23 fields...")

	if license.LicenseType == "" {
		fmt.Println("❌ ERROR: license_type field is missing!")
	} else {
		fmt.Printf("✅ license_type: %s\n", license.LicenseType)
	}

	if license.StartDate == "" {
		fmt.Println("❌ ERROR: start_date field is missing!")
	} else {
		fmt.Printf("✅ start_date: %s\n", license.StartDate)
	}

	// 显示完整的license结构
	fmt.Println("\n📄 Generated License Structure:")
	fmt.Println("================================")

	licenseJSON, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal license to JSON: %v", err)
	}

	fmt.Println(string(licenseJSON))

	// 保存到测试文件
	testFileName := "test_v23_license.json"
	err = SaveLicenseToFile(license, testFileName)
	if err != nil {
		log.Fatalf("Failed to save test license: %v", err)
	}

	fmt.Printf("\n✅ Test license saved to: %s\n", testFileName)

	// 验证签名是否包含新字段
	fmt.Println("\n🔐 Verifying signature includes V23 fields...")

	// 这里我们可以通过解析签名数据来验证
	// 但为了简化，我们通过检查license结构来确认
	if license.LicenseType != "" && license.StartDate != "" && license.Signature != "" {
		fmt.Println("✅ Signature should include V23 fields (license_type and start_date)")
		fmt.Printf("✅ Signature length: %d characters\n", len(license.Signature))
	} else {
		fmt.Println("❌ ERROR: Missing fields for signature generation")
	}

	fmt.Println("\n🎉 V23 License Generation Test Complete!")
}
