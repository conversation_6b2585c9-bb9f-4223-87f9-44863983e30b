// 测试V11最终修复版本
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}
	
	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}
	
	return privateKey, nil
}

func validateLicense(filename string) error {
	fmt.Printf("🔍 Validating %s\n", filename)
	fmt.Println("=" + fmt.Sprintf("%*s", len(filename)+15, "="))
	
	// 1. 加载许可证
	licenseData, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read license: %v", err)
	}
	
	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		return fmt.Errorf("failed to parse license: %v", err)
	}
	
	fmt.Printf("📋 License Info:\n")
	fmt.Printf("  Company: %s\n", license.CompanyName)
	fmt.Printf("  Email: %s\n", license.Email)
	fmt.Printf("  Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  Expiration: %s\n", license.ExpirationDate)
	fmt.Printf("  Issued: %s\n", license.IssuedDate)
	
	// 2. 加载私钥
	privateKey, err := loadPrivateKey()
	if err != nil {
		return fmt.Errorf("failed to load private key: %v", err)
	}
	
	// 3. 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}
	
	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}
	
	fmt.Printf("🔓 Decrypted Machine ID: %s\n", string(decryptedMachineID))
	
	// 4. 重构签名数据
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}
	
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}
	
	fmt.Printf("📋 Signature JSON: %s\n", string(jsonData))
	
	// 5. 验证签名
	hash := sha256.Sum256(jsonData)
	
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}
	
	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	
	fmt.Println("✅ Signature verification successful!")
	return nil
}

func compareWithWorkingVersion() {
	fmt.Println("\n🔍 Comparing with Known Working Version")
	fmt.Println("=======================================")
	
	// 加载工作版本
	workingData, err := os.ReadFile("fresh_factory_license.json")
	if err != nil {
		fmt.Printf("❌ Cannot load working version: %v\n", err)
		return
	}
	
	var workingLicense LicenseData
	err = json.Unmarshal(workingData, &workingLicense)
	if err != nil {
		fmt.Printf("❌ Cannot parse working version: %v\n", err)
		return
	}
	
	// 加载GUI生成的版本
	guiData, err := os.ReadFile("factory_license.json")
	if err != nil {
		fmt.Printf("❌ Cannot load GUI version: %v\n", err)
		return
	}
	
	var guiLicense LicenseData
	err = json.Unmarshal(guiData, &guiLicense)
	if err != nil {
		fmt.Printf("❌ Cannot parse GUI version: %v\n", err)
		return
	}
	
	fmt.Printf("📋 Comparison:\n")
	fmt.Printf("  Working Version Encrypted ID: %s...\n", workingLicense.EncryptedMachineID[:50])
	fmt.Printf("  GUI Version Encrypted ID:     %s...\n", guiLicense.EncryptedMachineID[:50])
	
	if workingLicense.EncryptedMachineID == guiLicense.EncryptedMachineID {
		fmt.Println("✅ Encrypted Machine IDs match!")
		fmt.Println("   This means V11 fix is working correctly.")
	} else {
		fmt.Println("❌ Encrypted Machine IDs differ")
		fmt.Println("   V11 fix may need further adjustment.")
	}
	
	// 解密两个版本的机器ID进行比较
	privateKey, err := loadPrivateKey()
	if err != nil {
		fmt.Printf("❌ Cannot load private key: %v\n", err)
		return
	}
	
	// 解密工作版本
	workingEncrypted, _ := base64.StdEncoding.DecodeString(workingLicense.EncryptedMachineID)
	workingDecrypted, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, workingEncrypted, nil)
	if err != nil {
		fmt.Printf("❌ Cannot decrypt working version: %v\n", err)
		return
	}
	
	// 解密GUI版本
	guiEncrypted, _ := base64.StdEncoding.DecodeString(guiLicense.EncryptedMachineID)
	guiDecrypted, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, guiEncrypted, nil)
	if err != nil {
		fmt.Printf("❌ Cannot decrypt GUI version: %v\n", err)
		return
	}
	
	fmt.Printf("\n🔓 Decrypted Machine IDs:\n")
	fmt.Printf("  Working Version: %s\n", string(workingDecrypted))
	fmt.Printf("  GUI Version:     %s\n", string(guiDecrypted))
	
	if string(workingDecrypted) == string(guiDecrypted) {
		fmt.Println("✅ Both decrypt to the same raw machine ID!")
		fmt.Println("   This confirms the fix is working correctly.")
	} else {
		fmt.Println("❌ Different raw machine IDs")
		fmt.Println("   This indicates a problem with the fix.")
	}
}

func main() {
	fmt.Println("🚀 Testing V11 Final Fix")
	fmt.Println("========================")
	
	fmt.Println("Please use license-generator-v11-final-fix.exe to generate a new factory_license.json")
	fmt.Println("Then this script will validate it and compare with the working version.")
	fmt.Println("")
	
	// 测试已知工作的版本
	if _, err := os.Stat("fresh_factory_license.json"); err == nil {
		fmt.Println("🧪 Testing Known Working Version...")
		err := validateLicense("fresh_factory_license.json")
		if err != nil {
			fmt.Printf("❌ Working version failed: %v\n", err)
		} else {
			fmt.Println("✅ Working version validation successful!")
		}
	}
	
	// 测试GUI生成的版本
	if _, err := os.Stat("factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing GUI Generated Version...")
		err := validateLicense("factory_license.json")
		if err != nil {
			fmt.Printf("❌ GUI version failed: %v\n", err)
		} else {
			fmt.Println("✅ GUI version validation successful!")
		}
	}
	
	// 比较两个版本
	compareWithWorkingVersion()
	
	fmt.Println("\n📋 Test Summary:")
	fmt.Println("- If both versions pass validation, V11 fix is successful")
	fmt.Println("- If encrypted machine IDs match, the fix is working correctly")
	fmt.Println("- If raw machine IDs match, the underlying logic is consistent")
}
