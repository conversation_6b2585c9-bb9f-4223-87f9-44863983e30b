// correct_key_extractor.go
// 正确提取两对完全独立的密钥

package main

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔧 Correct Key Extraction for Two Separate Key Pairs")
	fmt.Println("===================================================")

	// 1. 提取机器ID解密密钥对
	fmt.Println("📝 1. Extracting Machine ID Decryption Key Pair...")
	err := extractMachineDecryptionKeyPair()
	if err != nil {
		fmt.Printf("❌ Failed to extract machine decryption key pair: %v\n", err)
		return
	}

	// 2. 提取签名密钥对 (正确方法)
	fmt.Println("📝 2. Extracting Signature Key Pair (Correct Method)...")
	err = extractSignatureKeyPair()
	if err != nil {
		fmt.Printf("❌ Failed to extract signature key pair: %v\n", err)
		return
	}

	// 3. 验证两对密钥确实不同
	fmt.Println("📝 3. Verifying Key Pairs Are Different...")
	err = verifyKeyPairsSeparation()
	if err != nil {
		fmt.Printf("❌ Key pairs verification failed: %v\n", err)
		return
	}

	fmt.Println("\n🎉 Success! Two separate key pairs extracted correctly!")
	fmt.Println("\n📋 Generated Files:")
	fmt.Println("===================")
	fmt.Println("🔑 machine_id_decryption_private_key_CORRECT.pem  - 机器ID解密私钥")
	fmt.Println("🔓 machine_id_decryption_public_key_CORRECT.pem   - 机器ID解密公钥")
	fmt.Println("✍️  signature_generation_private_key_CORRECT.pem   - 签名生成私钥")
	fmt.Println("🔍 signature_verification_public_key_CORRECT.pem  - 签名验证公钥")

	fmt.Println("\n🎯 Key Usage:")
	fmt.Println("=============")
	fmt.Println("🔐 Machine ID Key Pair:")
	fmt.Println("   Private: 解密机器绑定信息")
	fmt.Println("   Public:  加密机器绑定信息")
	fmt.Println()
	fmt.Println("✍️  Digital Signature Key Pair:")
	fmt.Println("   Private: 创建数字签名")
	fmt.Println("   Public:  验证数字签名")
}

// 提取机器ID解密密钥对
func extractMachineDecryptionKeyPair() error {
	// 从当前使用的机器解密密钥文件读取
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return fmt.Errorf("failed to read machine decryption key: %v", err)
	}

	// 解析私钥
	block, _ := pem.Decode(keyData)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %v", err)
	}

	// 保存机器ID解密私钥
	privateKeyPEM := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}

	privateKeyFile, err := os.Create("machine_id_decryption_private_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to create private key file: %v", err)
	}
	defer privateKeyFile.Close()

	err = pem.Encode(privateKeyFile, privateKeyPEM)
	if err != nil {
		return fmt.Errorf("failed to write private key: %v", err)
	}

	// 从私钥提取对应的公钥
	publicKey := &privateKey.PublicKey
	publicKeyPKCS1 := x509.MarshalPKCS1PublicKey(publicKey)

	publicKeyPEM := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyPKCS1,
	}

	publicKeyFile, err := os.Create("machine_id_decryption_public_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to create public key file: %v", err)
	}
	defer publicKeyFile.Close()

	err = pem.Encode(publicKeyFile, publicKeyPEM)
	if err != nil {
		return fmt.Errorf("failed to write public key: %v", err)
	}

	fmt.Println("✅ Machine ID decryption key pair extracted")
	return nil
}

// 提取签名密钥对 (正确方法：从私钥提取公钥)
func extractSignatureKeyPair() error {
	// 从license_signing_private_key.pem读取签名私钥
	keyData, err := os.ReadFile("license_signing_private_key.pem")
	if err != nil {
		return fmt.Errorf("failed to read license signing private key: %v", err)
	}

	// 解析私钥
	block, _ := pem.Decode(keyData)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %v", err)
	}

	// 保存签名生成私钥
	err = os.WriteFile("signature_generation_private_key_CORRECT.pem", keyData, 0600)
	if err != nil {
		return fmt.Errorf("failed to write signature generation private key: %v", err)
	}

	// 从私钥提取对应的公钥 (这是关键!)
	publicKey := &privateKey.PublicKey
	publicKeyPKCS1 := x509.MarshalPKCS1PublicKey(publicKey)

	publicKeyPEM := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyPKCS1,
	}

	publicKeyFile, err := os.Create("signature_verification_public_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to create signature verification public key file: %v", err)
	}
	defer publicKeyFile.Close()

	err = pem.Encode(publicKeyFile, publicKeyPEM)
	if err != nil {
		return fmt.Errorf("failed to write signature verification public key: %v", err)
	}

	fmt.Println("✅ Signature key pair extracted (from private key)")
	return nil
}

// 验证两对密钥确实分离
func verifyKeyPairsSeparation() error {
	// 加载机器密钥对
	machinePrivData, err := os.ReadFile("machine_id_decryption_private_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to read machine private key: %v", err)
	}

	machinePrivBlock, _ := pem.Decode(machinePrivData)
	machinePrivKey, err := x509.ParsePKCS1PrivateKey(machinePrivBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse machine private key: %v", err)
	}

	// 加载签名密钥对
	signaturePrivData, err := os.ReadFile("signature_generation_private_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to read signature private key: %v", err)
	}

	signaturePrivBlock, _ := pem.Decode(signaturePrivData)
	signaturePrivKey, err := x509.ParsePKCS1PrivateKey(signaturePrivBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse signature private key: %v", err)
	}

	// 检查两个私钥是否不同
	if machinePrivKey.N.Cmp(signaturePrivKey.N) == 0 {
		return fmt.Errorf("PROBLEM: Machine and signature private keys are the same!")
	}

	// 加载公钥并验证
	machinePubData, err := os.ReadFile("machine_id_decryption_public_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to read machine public key: %v", err)
	}

	machinePubBlock, _ := pem.Decode(machinePubData)
	machinePubKey, err := x509.ParsePKCS1PublicKey(machinePubBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse machine public key: %v", err)
	}

	signaturePubData, err := os.ReadFile("signature_verification_public_key_CORRECT.pem")
	if err != nil {
		return fmt.Errorf("failed to read signature public key: %v", err)
	}

	signaturePubBlock, _ := pem.Decode(signaturePubData)
	signaturePubKey, err := x509.ParsePKCS1PublicKey(signaturePubBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse signature public key: %v", err)
	}

	// 验证密钥对匹配
	machinePrivPubKey := &machinePrivKey.PublicKey
	signaturePrivPubKey := &signaturePrivKey.PublicKey

	if machinePrivPubKey.N.Cmp(machinePubKey.N) != 0 {
		return fmt.Errorf("machine private and public keys don't match")
	}

	if signaturePrivPubKey.N.Cmp(signaturePubKey.N) != 0 {
		return fmt.Errorf("signature private and public keys don't match")
	}

	// 验证两对密钥不同
	if machinePubKey.N.Cmp(signaturePubKey.N) == 0 {
		return fmt.Errorf("PROBLEM: Machine and signature public keys are the same!")
	}

	// 关键检查：机器私钥不应该与签名公钥配对
	if machinePrivPubKey.N.Cmp(signaturePubKey.N) == 0 {
		return fmt.Errorf("CRITICAL: Machine private key matches signature public key!")
	}

	fmt.Println("✅ Key pairs verification successful:")
	fmt.Println("   - Machine key pair is valid")
	fmt.Println("   - Signature key pair is valid")
	fmt.Println("   - Two key pairs are completely different")
	fmt.Println("   - No cross-matching between pairs")

	return nil
}
