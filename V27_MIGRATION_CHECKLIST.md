# V27 Migration Checklist for Authorized Software
# V27被授权软件迁移检查清单

## 📋 Pre-Migration Assessment / 迁移前评估

### Current State Check / 当前状态检查
- [ ] Identify current license validation code location
- [ ] Document current license format being used
- [ ] List all places where license data is accessed
- [ ] Backup current working validator code

- [ ] 识别当前许可证验证代码位置
- [ ] 记录当前使用的许可证格式
- [ ] 列出访问许可证数据的所有位置
- [ ] 备份当前工作的验证器代码

### V27 Readiness Check / V27准备检查
- [ ] Confirm you have V27 license generator available
- [ ] Verify you have access to all three required keys
- [ ] Test V27 license generation process
- [ ] Obtain sample V27 license files for testing

- [ ] 确认您有V27许可证生成器可用
- [ ] 验证您可以访问所有三个必需的密钥
- [ ] 测试V27许可证生成过程
- [ ] 获取V27许可证文件样本用于测试

## 🔑 Key Management / 密钥管理

### Key Collection / 密钥收集
- [ ] Obtain `MACHINE_ID_DECRYPTION_PUBLIC_KEY`
- [ ] Obtain `SIGNATURE_VERIFICATION_PUBLIC_KEY`
- [ ] Obtain `COMPANY_ID_DECRYPTION_PUBLIC_KEY` (NEW)
- [ ] Verify key formats are correct
- [ ] Test key loading in your application

### Key Integration / 密钥集成
- [ ] Embed all three keys as constants in your code
- [ ] Remove old hardcoded keys (if any)
- [ ] Test key loading functions
- [ ] Verify key parsing works correctly
- [ ] Implement secure key storage if needed

## 📝 Code Structure Updates / 代码结构更新

### Data Structure Changes / 数据结构修改
- [ ] Update `LicenseData` structure
  - [ ] Add `EncryptedDataBlock string` field
  - [ ] Map to JSON field `"encrypted_data_block"`
  - [ ] Test JSON parsing with new field

- [ ] Update `SignatureData` structure
  - [ ] Add `CompanyIDHash string` field
  - [ ] Map to JSON field `"c"`
  - [ ] Test signature data creation

### Function Updates / 函数更新
- [ ] Add company ID decryption function
- [ ] Update signature verification function
- [ ] Add company ID validation logic
- [ ] Update license loading function
- [ ] Add error handling for new fields

## 🔧 Implementation Steps / 实现步骤

### Phase 1: Basic Integration / 阶段1：基本集成
- [ ] Add new data structure fields
- [ ] Implement company ID decryption
- [ ] Update signature verification
- [ ] Test with sample V27 license
- [ ] Verify all validations pass

### Phase 2: Enhanced Features / 阶段2：增强功能
- [ ] Implement company-specific features
- [ ] Add company ID range detection
- [ ] Create feature enablement logic
- [ ] Test different company ID ranges
- [ ] Validate feature activation

### Phase 3: Error Handling / 阶段3：错误处理
- [ ] Add specific error messages for company ID issues
- [ ] Handle missing `encrypted_data_block` field
- [ ] Implement graceful degradation if needed
- [ ] Add logging for debugging
- [ ] Test error scenarios

## 🧪 Testing Protocol / 测试协议

### Unit Tests / 单元测试
- [ ] Test company ID decryption with valid data
- [ ] Test company ID decryption with invalid data
- [ ] Test signature verification with company ID
- [ ] Test signature verification without company ID
- [ ] Test license loading with V27 format

### Integration Tests / 集成测试
- [ ] Test complete license validation flow
- [ ] Test with different company ID ranges
- [ ] Test machine binding validation
- [ ] Test license expiration validation
- [ ] Test with corrupted license files

### Compatibility Tests / 兼容性测试
- [ ] Test V27 license validation (should work)
- [ ] Test V26 license validation (should fail gracefully)
- [ ] Test invalid license formats
- [ ] Test missing fields scenarios
- [ ] Test with different machine IDs

## 🔒 Security Validation / 安全验证

### Encryption Tests / 加密测试
- [ ] Verify company ID is properly decrypted
- [ ] Test with wrong decryption key (should fail)
- [ ] Verify encrypted data cannot be read without key
- [ ] Test key format validation
- [ ] Verify no plaintext company ID exposure

### Signature Tests / 签名测试
- [ ] Verify signature includes company ID hash
- [ ] Test with modified company ID (should fail)
- [ ] Test with modified license data (should fail)
- [ ] Verify signature verification key works
- [ ] Test signature tampering detection

### Access Control Tests / 访问控制测试
- [ ] Verify only authorized code can access keys
- [ ] Test key storage security
- [ ] Verify no key leakage in logs
- [ ] Test secure key loading
- [ ] Validate key usage restrictions

## 🚀 Deployment Preparation / 部署准备

### Code Review / 代码审查
- [ ] Review all code changes
- [ ] Verify security best practices
- [ ] Check error handling completeness
- [ ] Validate logging and monitoring
- [ ] Ensure no debug code remains

### Documentation / 文档
- [ ] Update internal documentation
- [ ] Document new company ID features
- [ ] Create troubleshooting guide
- [ ] Update user manuals if needed
- [ ] Document migration process

### Deployment Package / 部署包
- [ ] Include all three public keys
- [ ] Package updated validator code
- [ ] Include sample V27 licenses for testing
- [ ] Prepare rollback plan
- [ ] Create deployment checklist

## ✅ Validation Checklist / 验证检查清单

### Pre-Deployment Testing / 部署前测试
- [ ] Test in development environment
- [ ] Test in staging environment
- [ ] Perform load testing if applicable
- [ ] Test with real V27 licenses
- [ ] Verify all company ID ranges work

### Post-Deployment Verification / 部署后验证
- [ ] Verify license validation works in production
- [ ] Check company-specific features activate correctly
- [ ] Monitor for any validation errors
- [ ] Verify performance is acceptable
- [ ] Confirm no security issues

### User Acceptance / 用户验收
- [ ] Test with end users
- [ ] Verify user experience is smooth
- [ ] Check feature availability
- [ ] Validate error messages are clear
- [ ] Confirm help documentation is adequate

## 🆘 Rollback Plan / 回滚计划

### Rollback Triggers / 回滚触发条件
- [ ] License validation failures
- [ ] Performance degradation
- [ ] Security vulnerabilities discovered
- [ ] User experience issues
- [ ] Critical bugs found

### Rollback Procedure / 回滚程序
- [ ] Restore previous validator code
- [ ] Revert to old license format support
- [ ] Restore old public keys
- [ ] Test rollback in staging first
- [ ] Communicate changes to users

## 📊 Success Metrics / 成功指标

### Technical Metrics / 技术指标
- [ ] License validation success rate > 99%
- [ ] Company ID decryption success rate > 99%
- [ ] Signature verification success rate > 99%
- [ ] Performance within acceptable limits
- [ ] No security incidents

### Business Metrics / 业务指标
- [ ] User satisfaction maintained
- [ ] Feature adoption rate
- [ ] Support ticket volume
- [ ] System uptime maintained
- [ ] Compliance requirements met

## 📞 Support Resources / 支持资源

### Documentation / 文档资源
- [ ] `AUTHORIZED_SOFTWARE_INTEGRATION_GUIDE.md`
- [ ] `V27_QUICK_REFERENCE_FOR_AUTHORIZED_SOFTWARE.md`
- [ ] `license_validator_v27_with_company_id.go`
- [ ] `public_keys_for_authorized_software.go`

### Testing Resources / 测试资源
- [ ] Sample V27 license files
- [ ] Test company ID ranges
- [ ] Validation test cases
- [ ] Performance benchmarks

### Emergency Contacts / 紧急联系
- [ ] Technical lead contact information
- [ ] Security team contact information
- [ ] License generator team contact
- [ ] Escalation procedures documented

---

**Migration Checklist Version**: V27.1  
**Last Updated**: 2025-07-13  
**Status**: Ready for Use  
**Estimated Migration Time**: 2-5 days depending on complexity
