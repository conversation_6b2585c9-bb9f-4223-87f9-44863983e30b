// key_extractor.go
// 独立的密钥提取工具

package main

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔐 Extracting All Keys for License System")
	fmt.Println("=========================================")

	// 1. 机器ID解密密钥 (当前使用的密钥)
	fmt.Println("📝 1. Extracting Machine ID Decryption Key...")
	err := extractMachineDecryptionKey()
	if err != nil {
		fmt.Printf("❌ Failed to extract machine decryption key: %v\n", err)
		return
	}

	// 2. 签名生成密钥 (新的签名密钥)
	fmt.Println("📝 2. Extracting Signature Generation Key...")
	err = extractSignatureGenerationKey()
	if err != nil {
		fmt.Printf("❌ Failed to extract signature generation key: %v\n", err)
		return
	}

	// 3. 签名验证密钥 (从V23验证器中提取)
	fmt.Println("📝 3. Extracting Signature Verification Key...")
	err = extractSignatureVerificationKey()
	if err != nil {
		fmt.Printf("❌ Failed to extract signature verification key: %v\n", err)
		return
	}

	fmt.Println("\n🎉 All keys extracted successfully!")
	fmt.Println("\n📋 Generated Files:")
	fmt.Println("===================")
	fmt.Println("🔑 machine_id_decryption_private_key.pem  - 机器ID解密私钥")
	fmt.Println("🔓 machine_id_decryption_public_key.pem   - 机器ID解密公钥")
	fmt.Println("✍️  signature_generation_private_key.pem   - 签名生成私钥")
	fmt.Println("🔍 signature_verification_public_key.pem  - 签名验证公钥")

	fmt.Println("\n🎯 Key Usage:")
	fmt.Println("=============")
	fmt.Println("🔐 Machine ID Decryption:")
	fmt.Println("   Private: 解密机器绑定信息")
	fmt.Println("   Public:  加密机器绑定信息")
	fmt.Println()
	fmt.Println("✍️  Digital Signature:")
	fmt.Println("   Private: 创建数字签名")
	fmt.Println("   Public:  验证数字签名")
}

// 提取机器ID解密密钥
func extractMachineDecryptionKey() error {
	// 从当前使用的机器解密密钥文件读取
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return fmt.Errorf("failed to read machine decryption key: %v", err)
	}

	// 解析私钥
	block, _ := pem.Decode(keyData)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %v", err)
	}

	// 保存机器ID解密私钥
	privateKeyPEM := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}

	privateKeyFile, err := os.Create("machine_id_decryption_private_key.pem")
	if err != nil {
		return fmt.Errorf("failed to create private key file: %v", err)
	}
	defer privateKeyFile.Close()

	err = pem.Encode(privateKeyFile, privateKeyPEM)
	if err != nil {
		return fmt.Errorf("failed to write private key: %v", err)
	}

	// 保存机器ID解密公钥
	publicKey := &privateKey.PublicKey
	publicKeyPKCS1 := x509.MarshalPKCS1PublicKey(publicKey)

	publicKeyPEM := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyPKCS1,
	}

	publicKeyFile, err := os.Create("machine_id_decryption_public_key.pem")
	if err != nil {
		return fmt.Errorf("failed to create public key file: %v", err)
	}
	defer publicKeyFile.Close()

	err = pem.Encode(publicKeyFile, publicKeyPEM)
	if err != nil {
		return fmt.Errorf("failed to write public key: %v", err)
	}

	fmt.Println("✅ Machine ID decryption keys extracted")
	return nil
}

// 提取签名生成密钥
func extractSignatureGenerationKey() error {
	// 从license_signing_private_key.pem读取
	keyData, err := os.ReadFile("license_signing_private_key.pem")
	if err != nil {
		return fmt.Errorf("failed to read license_signing_private_key.pem: %v", err)
	}

	// 直接复制到新文件名
	err = os.WriteFile("signature_generation_private_key.pem", keyData, 0600)
	if err != nil {
		return fmt.Errorf("failed to write signature generation private key: %v", err)
	}

	fmt.Println("✅ Signature generation private key extracted")
	return nil
}

// 提取签名验证密钥
func extractSignatureVerificationKey() error {
	// 从V23验证器中提取公钥
	signingPublicKey := `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

	err := os.WriteFile("signature_verification_public_key.pem", []byte(signingPublicKey), 0644)
	if err != nil {
		return fmt.Errorf("failed to write signature verification public key: %v", err)
	}

	fmt.Println("✅ Signature verification public key extracted")
	return nil
}
