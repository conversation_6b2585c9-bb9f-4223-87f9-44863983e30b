# Signature Generation Code Reference for Authorized Software
# 被授权软件签名生成代码参考

## 📍 **Key Files to Review / 需要查看的关键文件**

被授权软件开发者应该查看以下文件来理解V27签名生成的确切实现：

### 1. **crypto.go** - 核心签名函数 (第108-140行)

**最重要的函数**: `CreateSignature()`

<augment_code_snippet path="crypto.go" mode="EXCERPT">
````go
// CreateSignature creates a digital signature for the license data
// Note: Company name, email, and phone are NOT included in signature verification
func CreateSignature(licenseData *LicenseData, privateKey *rsa.PrivateKey, startTime, expirationTime time.Time, machineID, companyID string) (string, error) {
	// Create compact signature data to fit within RSA2048 limits
	// Excludes company name, email, and phone from signature verification
	sigData := SignatureData{
		Software:       licenseData.AuthorizedSoftware, // Software name only
		Version:        licenseData.AuthorizedVersion,  // Software version only
		LicenseType:    licenseData.LicenseType,        // License type
		StartUnix:      startTime.Unix(),               // Start date as Unix timestamp
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(machineID), // Use provided machine ID
		CompanyIDHash:  hashString(companyID), // Use provided company ID (7 digits, no hyphen, e.g., "1234567")
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// Create hash of the data
	hash := sha256.Sum256(jsonData)

	// Sign the hash
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to create signature: %v", err)
	}

	// Return base64 encoded signature
	return base64.StdEncoding.EncodeToString(signature), nil
}
````
</augment_code_snippet>

### 2. **models.go** - 签名数据结构 (第49-57行)

**关键结构**: `SignatureData`

<augment_code_snippet path="models.go" mode="EXCERPT">
````go
// SignatureData represents the data used to create the signature
// This should be compact to fit within RSA2048 size limits
// Note: Company name, email, and phone are NOT included in signature verification
type SignatureData struct {
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	StartUnix      int64  `json:"b"` // Start date as Unix timestamp (shortened key: "b" for begin)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	CompanyIDHash  string `json:"c"` // Hash of company ID (shortened key)
}
````
</augment_code_snippet>

### 3. **crypto.go** - 哈希函数 (第189-193行)

**哈希函数**: `hashString()`

<augment_code_snippet path="crypto.go" mode="EXCERPT">
````go
// hashString creates a SHA256 hash of the input string
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return base64.StdEncoding.EncodeToString(hash[:])
}
````
</augment_code_snippet>

### 4. **license.go** - 签名调用 (第106行)

**调用位置**: `GenerateLicense()` 函数中

<augment_code_snippet path="license.go" mode="EXCERPT">
````go
// Use separate signing key for digital signature creation
signature, err := CreateSignature(license, lg.signingKey, licenseStartTime, licenseExpirationTime, rawMachineID, companyID)
if err != nil {
    return nil, fmt.Errorf("failed to create signature: %v", err)
}
````
</augment_code_snippet>

## 🔍 **签名生成流程详解**

### Step 1: 创建签名数据结构
```go
sigData := SignatureData{
    Software:       "LS-DYNA Model License Generate Factory",  // 软件名称
    Version:        "2.3.0",                                   // 软件版本
    LicenseType:    "lease",                                   // 许可证类型
    StartUnix:      **********,                                // 开始日期Unix时间戳
    ExpirationUnix: **********,                                // 到期日期Unix时间戳
    MachineIDHash:  "base64_hash_of_machine_id",               // 机器ID哈希
    CompanyIDHash:  "base64_hash_of_company_id",               // 公司ID哈希 (NEW)
}
```

### Step 2: 转换为JSON
```json
{
  "s": "LS-DYNA Model License Generate Factory",
  "v": "2.3.0",
  "t": "lease",
  "b": **********,
  "x": **********,
  "m": "base64_hash_of_machine_id",
  "c": "base64_hash_of_company_id"
}
```

### Step 3: 创建SHA256哈希
```go
hash := sha256.Sum256(jsonData)
```

### Step 4: RSA签名
```go
signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
```

### Step 5: Base64编码
```go
return base64.StdEncoding.EncodeToString(signature)
```

## ⚠️ **被授权软件必须注意的关键点**

### 1. **公司ID格式**
- ✅ **正确**: `"1234567"` (7位数字，无短横线)
- ❌ **错误**: `"123-4567"` (带短横线)

### 2. **哈希函数**
- 必须使用相同的 `hashString()` 函数
- SHA256 + Base64编码
- 如果哈希长度超过16字符，截取前16字符

### 3. **JSON字段名**
- 使用缩短的字段名 (`s`, `v`, `t`, `b`, `x`, `m`, `c`)
- 字段顺序必须一致
- JSON序列化必须产生相同结果

### 4. **时间戳格式**
- 使用Unix时间戳 (int64)
- 从 "2006-01-02" 格式解析日期
- 使用 `time.Unix()` 转换

### 5. **签名算法**
- RSA PKCS1v15 签名
- SHA256 哈希算法
- Base64编码最终签名

## 🧪 **验证测试建议**

### 测试用例1: 基本签名验证
```go
// 使用已知数据测试
machineID := "test-machine-id"
companyID := "1234567"
software := "Test Software"
version := "1.0.0"
licenseType := "lease"
startTime := time.Date(2025, 1, 13, 0, 0, 0, 0, time.UTC)
expirationTime := time.Date(2026, 1, 13, 0, 0, 0, 0, time.UTC)

// 创建签名数据并验证
```

### 测试用例2: 哈希函数验证
```go
// 验证哈希函数产生相同结果
hash1 := hashString("1234567")
hash2 := hashString("1234567")
assert.Equal(t, hash1, hash2)

// 验证不同输入产生不同哈希
hash3 := hashString("1234568")
assert.NotEqual(t, hash1, hash3)
```

### 测试用例3: JSON序列化验证
```go
// 验证JSON序列化产生预期格式
sigData := SignatureData{...}
jsonData, _ := json.Marshal(sigData)
// 检查JSON格式是否正确
```

## 📋 **被授权软件实现检查清单**

- [ ] 复制完全相同的 `SignatureData` 结构
- [ ] 实现相同的 `hashString()` 函数
- [ ] 使用相同的JSON字段名 (`s`, `v`, `t`, `b`, `x`, `m`, `c`)
- [ ] 确保公司ID格式为7位数字无短横线
- [ ] 使用相同的时间戳转换方式
- [ ] 实现相同的RSA签名验证算法
- [ ] 测试与生成器产生的签名匹配

## 🔗 **相关文件链接**

- `crypto.go` - 完整的加密和签名函数
- `models.go` - 所有数据结构定义
- `license.go` - 许可证生成逻辑
- `license_validator_v27_with_company_id.go` - 完整的验证器示例

## 💡 **调试建议**

如果签名验证失败，检查以下项目：

1. **JSON输出**: 打印序列化的JSON，确保格式完全匹配
2. **哈希值**: 比较机器ID和公司ID的哈希值
3. **时间戳**: 验证Unix时间戳计算正确
4. **公司ID格式**: 确保是7位数字，无短横线
5. **字段顺序**: 虽然JSON字段顺序理论上不重要，但保持一致更安全

---

**参考版本**: V27  
**最后更新**: 2025-07-13  
**状态**: 生产就绪  
**重要性**: 🔴 关键 - 签名验证失败将导致许可证无效
