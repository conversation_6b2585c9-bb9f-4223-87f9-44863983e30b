# License Validator for Authorized Software
# 被授权软件许可证验证器

## 🎯 Quick Start / 快速开始

You have received the following files for license validation:
您收到了以下用于许可证验证的文件：

```
📁 License Validation Package
├── standalone_license_validator.go           # 主验证器文件
├── LICENSE_VALIDATOR_INTEGRATION_GUIDE.md    # 详细集成指南
├── MACHINE_ID_IMPLEMENTATION_GUIDE.md        # 机器ID实现指南
├── example_integration.go                    # 完整集成示例
└── README_FOR_AUTHORIZED_SOFTWARE.md         # 本文件
```

## ⚡ 5-Minute Integration / 5分钟集成

### Step 1: Copy the Validator / 复制验证器
```bash
cp standalone_license_validator.go your_project/license_validator.go
```

### Step 2: Change Package Name / 修改包名
```go
// In license_validator.go, change:
package main
// To:
package yourapp
```

### Step 3: Basic Usage / 基本使用
```go
package main

import "log"

func main() {
    // Validate license on startup
    err := ValidateLicenseFile("factory_license.json")
    if err != nil {
        log.Fatal("License validation failed:", err)
    }
    
    log.Println("License validated successfully!")
    // Your application code here...
}
```

## ⚠️ CRITICAL: Machine ID Implementation / 关键：机器ID实现

**YOU MUST IMPLEMENT THE `getCurrentMachineID()` FUNCTION**

**您必须实现 `getCurrentMachineID()` 函数**

The function must return the SAME machine ID used when generating your license.
该函数必须返回生成许可证时使用的相同机器ID。

```go
func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
    // TODO: Replace this with your actual machine ID logic
    // 待办：用您的实际机器ID逻辑替换此处
    
    // Example format (customize based on your needs):
    // 示例格式（根据您的需求自定义）：
    cpuID := "your-cpu-id-here"
    diskSerial := "your-disk-serial-here"
    machineID := fmt.Sprintf("%s-%s", cpuID, diskSerial)
    
    return machineID, nil
}
```

📖 **See `MACHINE_ID_IMPLEMENTATION_GUIDE.md` for detailed implementation examples.**

## 🔒 Security Features / 安全特性

The license validator provides:
许可证验证器提供：

- ✅ **Machine Binding**: License tied to specific hardware
- ✅ **Digital Signature**: Prevents tampering
- ✅ **Expiration Check**: Automatic date validation
- ✅ **Encrypted Machine ID**: Secure machine identification

- ✅ **机器绑定**: 许可证绑定到特定硬件
- ✅ **数字签名**: 防止篡改
- ✅ **过期检查**: 自动日期验证
- ✅ **加密机器ID**: 安全的机器识别

## 📋 License File Format / 许可证文件格式

Your customers will provide a `factory_license.json` file with this structure:
您的客户将提供具有以下结构的 `factory_license.json` 文件：

```json
{
  "company_name": "Customer Company",
  "email": "<EMAIL>",
  "phone": "************",
  "authorized_software": "Your Software Name",
  "authorized_version": "1.0.0",
  "expiration_date": "2025-12-31",
  "issued_date": "2025-01-01",
  "encrypted_machine_id": "base64-encoded-encrypted-data",
  "signature": "base64-encoded-digital-signature"
}
```

## 🧪 Testing / 测试

### Test License Validation / 测试许可证验证
```go
func TestLicenseValidation() {
    err := ValidateLicenseFile("factory_license.json")
    if err != nil {
        fmt.Printf("Validation failed: %v\n", err)
    } else {
        fmt.Println("Validation successful!")
    }
}
```

### Debug Machine ID / 调试机器ID
```go
func DebugMachineID() {
    validator, _ := NewLicenseValidator()
    machineID, err := validator.getCurrentMachineID()
    if err != nil {
        fmt.Printf("Error: %v\n", err)
    } else {
        fmt.Printf("Current Machine ID: %s\n", machineID)
    }
}
```

## 🚨 Common Error Messages / 常见错误消息

| Error Message | Cause | Solution |
|---------------|-------|----------|
| `license has expired` | License past expiration date | Contact provider for renewal |
| `license is not valid for this machine` | Machine ID mismatch | Check `getCurrentMachineID()` implementation |
| `signature verification failed` | File corrupted/tampered | Obtain new license file |
| `failed to read license file` | File not found/permissions | Check file path and permissions |

| 错误消息 | 原因 | 解决方案 |
|---------|------|----------|
| `license has expired` | 许可证已过期 | 联系提供商续期 |
| `license is not valid for this machine` | 机器ID不匹配 | 检查 `getCurrentMachineID()` 实现 |
| `signature verification failed` | 文件损坏/被篡改 | 获取新的许可证文件 |
| `failed to read license file` | 文件未找到/权限问题 | 检查文件路径和权限 |

## 🎯 Best Practices / 最佳实践

### 1. Validate Early / 早期验证
```go
func main() {
    // Validate license before starting main application
    if err := ValidateLicenseFile("factory_license.json"); err != nil {
        log.Fatal("License validation failed:", err)
    }
    
    startApplication()
}
```

### 2. Periodic Checks / 定期检查
```go
func startLicenseMonitor() {
    go func() {
        ticker := time.NewTicker(24 * time.Hour)
        for range ticker.C {
            if err := ValidateLicenseFile("factory_license.json"); err != nil {
                handleLicenseFailure(err)
            }
        }
    }()
}
```

### 3. Graceful Error Handling / 优雅的错误处理
```go
func handleLicenseFailure(err error) {
    switch {
    case strings.Contains(err.Error(), "expired"):
        showExpirationDialog()
    case strings.Contains(err.Error(), "machine binding"):
        showMachineBindingError()
    default:
        showGenericError(err)
    }
}
```

## 📚 Documentation / 文档

For detailed information, see:
详细信息请参阅：

- **`LICENSE_VALIDATOR_INTEGRATION_GUIDE.md`** - Complete integration guide with advanced examples
- **`MACHINE_ID_IMPLEMENTATION_GUIDE.md`** - Platform-specific machine ID implementation
- **`example_integration.go`** - Full working example with error handling

## 📞 Support / 技术支持

If you need help with integration:
如果您需要集成帮助：

1. **Check the error message** against the common errors table above
2. **Review the implementation guides** for detailed examples
3. **Test your machine ID implementation** using the debug functions
4. **Contact your license provider** with specific error messages and environment details

1. **检查错误消息** 对照上面的常见错误表
2. **查看实现指南** 获取详细示例
3. **测试您的机器ID实现** 使用调试函数
4. **联系您的许可证提供商** 提供具体的错误消息和环境详情

## ✅ Integration Checklist / 集成检查清单

- [ ] Copy `standalone_license_validator.go` to your project
- [ ] Change package name to match your project
- [ ] Implement `getCurrentMachineID()` function
- [ ] Test with your actual license file
- [ ] Add license validation to application startup
- [ ] Implement error handling for license failures
- [ ] Add periodic license checking (optional)
- [ ] Test on target deployment environment

- [ ] 将 `standalone_license_validator.go` 复制到您的项目
- [ ] 更改包名以匹配您的项目
- [ ] 实现 `getCurrentMachineID()` 函数
- [ ] 使用实际许可证文件进行测试
- [ ] 在应用程序启动时添加许可证验证
- [ ] 为许可证失败实现错误处理
- [ ] 添加定期许可证检查（可选）
- [ ] 在目标部署环境中测试

---

**🎉 You're ready to integrate license validation into your software!**

**🎉 您已准备好将许可证验证集成到您的软件中！**
