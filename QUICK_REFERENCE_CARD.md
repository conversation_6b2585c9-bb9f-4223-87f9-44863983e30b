# Factory License Generation - Quick Reference Card
# 工厂许可证生成 - 快速参考卡

## 🚀 Quick Start / 快速开始

### Required Files / 必需文件
```
✅ license-generator-v15-final-with-version.exe
✅ factory_machine_info.json
✅ machine_decryption_private_key_to_decryp_factory_machineinfo.pem
```

### 5-Step Process / 5步流程

#### 1️⃣ Launch / 启动
- Double-click `license-generator-v15-final-with-version.exe`
- Verify title shows "License Generator v15.0"

#### 2️⃣ Configure Files / 配置文件
- Browse → Select machine info JSON file
- Browse → Select private key PEM file

#### 3️⃣ Review Info / 检查信息
- Verify auto-filled company, email, phone
- Check software name and version
- Confirm decrypted machine ID

#### 4️⃣ Set Expiration / 设置过期
- **Recommended**: Use any preset option
- **Custom**: Enter YYYY-MM-DD format

#### 5️⃣ Generate / 生成
- Click "Generate License"
- Save as `factory_license.json`

## ✅ Success Checklist / 成功检查清单

### File Validation / 文件验证
- [ ] License file created (1-2KB size)
- [ ] Valid JSON structure
- [ ] Machine ID matches source file
- [ ] Future expiration date

### Compatibility Check / 兼容性检查
- [ ] Works with authorized software
- [ ] Signature verification passes
- [ ] User can verify machine binding

## ⚠️ Common Mistakes / 常见错误

### ❌ Wrong File Paths
**Problem**: File not found errors
**Solution**: Use Browse buttons, verify paths

### ❌ Corrupted JSON
**Problem**: Parse errors
**Solution**: Validate JSON syntax

### ❌ Wrong Expiration
**Problem**: Signature mismatch
**Solution**: Use preset options

### ❌ Permission Issues
**Problem**: Cannot save file
**Solution**: Run as administrator or change output folder

## 🔧 Troubleshooting / 故障排除

| Error Message | Solution |
|---------------|----------|
| "Failed to load machine info" | Check JSON file path and syntax |
| "Failed to load private key" | Verify PEM file format and permissions |
| "Signature verification failed" | Use preset expiration, regenerate license |
| "Invalid date format" | Use YYYY-MM-DD format for custom dates |

| 错误信息 | 解决方案 |
|----------|----------|
| "加载机器信息失败" | 检查JSON文件路径和语法 |
| "加载私钥失败" | 验证PEM文件格式和权限 |
| "签名验证失败" | 使用预设过期时间，重新生成许可证 |
| "日期格式无效" | 自定义日期使用YYYY-MM-DD格式 |

## 📋 File Format Reference / 文件格式参考

### Machine Info JSON / 机器信息JSON
```json
{
  "CompanyName": "Your Company",
  "Email": "<EMAIL>",
  "Phone": "Phone Number",
  "GeneratedBy": "Software Name v1.0.0",
  "MachineID": "EncryptedMachineID..."
}
```

### Generated License JSON / 生成的许可证JSON
```json
{
  "company_name": "Your Company",
  "email": "<EMAIL>",
  "phone": "Phone Number",
  "authorized_software": "Software Name",
  "authorized_version": "1.0.0",
  "expiration_date": "2026-01-10",
  "issued_date": "2025-07-10",
  "encrypted_machine_id": "SameMachineIDAsSource...",
  "signature": "DigitalSignature..."
}
```

## 🎯 Key Success Factors / 关键成功因素

### 1. Version Compatibility / 版本兼容性
- **Use v15.0** for latest compatibility
- **Check title bar** to confirm version

### 2. File Integrity / 文件完整性
- **Valid JSON** syntax in machine info
- **Correct PEM** format for private key
- **Proper encoding** (UTF-8 recommended)

### 3. Consistent Machine ID / 一致的机器ID
- **Same encrypted ID** in license as source
- **User verification** possible
- **Authorized software** compatibility

### 4. Proper Expiration / 正确的过期时间
- **Use presets** for compatibility
- **6-month calculation** internally
- **Future date** validation

## 📞 Quick Support / 快速支持

### Version Info / 版本信息
- **Current**: v15.0
- **Release**: 2025-07-10
- **Platform**: Windows

### File Locations / 文件位置
- **Executable**: `license-generator-v15-final-with-version.exe`
- **Config**: `config_license_generator_for_factory.json` (auto-created)
- **Output**: `factory_license.json` (user-specified)

### Success Indicators / 成功指标
```
✅ Window title shows "License Generator v15.0"
✅ Files load without errors
✅ Information auto-fills correctly
✅ License generates successfully
✅ Output file is valid JSON
✅ Machine ID consistency verified
```

---

**💡 Pro Tip**: Keep all files in the same folder for easier management!

**💡 专业提示**: 将所有文件保存在同一文件夹中，便于管理！
