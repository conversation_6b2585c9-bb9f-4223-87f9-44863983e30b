# License Validator Update Guide for Authorized Software
# 被授权软件许可证验证器更新指南

## 🎯 **更新目标**

将被授权软件的许可证验证器从单一密钥架构更新到分离密钥架构，支持V25版本生成的许可证。

## 🚨 **重要说明**

**当前问题**: 您的验证器使用旧的单一密钥架构，无法验证V25生成器创建的许可证。
**解决方案**: 更新到支持分离密钥的V23验证器。

## 📋 **更新前后对比**

### **当前架构 (需要更新)**
```
单一密钥对:
├── 公钥: 用于签名验证 + 机器ID加密
└── 私钥: 用于签名创建 + 机器ID解密

问题: 签名验证公钥 ↔ 机器ID解密私钥 是同一密钥对
```

### **新架构 (目标)**
```
机器绑定密钥对:
├── 公钥: 机器ID加密
└── 私钥: 机器ID解密

数字签名密钥对:
├── 公钥: 签名验证 (新密钥)
└── 私钥: 签名创建 (新密钥)

优势: 签名验证公钥 ↔ 机器ID解密私钥 是不同密钥对
```

## 🔧 **更新步骤**

### **步骤1: 备份当前验证器**
```bash
# 备份现有的验证器文件
cp your_current_validator.go your_current_validator_backup.go
```

### **步骤2: 获取新的V23验证器**
下载并使用提供的 `V23_LICENSE_VALIDATOR.go` 文件。

### **步骤3: 更新密钥配置**

#### **新的密钥常量**
在新验证器中，使用以下密钥配置：

```go
const (
    // 机器ID解密私钥 (保持不变)
    V23_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWs
BI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl
7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfC
n9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85j
j/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrk
Plt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQABAoIBAAGvHsEbie3RgKww
y2yX8TnJWcUoewAVn5zrkdMrsHmDO/szRb3n3EhEgJsYLQBRud7ejbAE3oLgKBe0
5vdmHmQfIOxc1gz9XGREC0oTCC6TgjsIH6S8OUVPBsqNyKZrecalLd/1u3GcO4qq
fuiC3UAHhKG5KEwXNoJlOPiZCp4UP80x3dEC+Fzc5l4Wd5AdcKiQzGg9bxdAmIbj
JEIqSCEOq+m2536zysSi9g7INDRj4yKwVIROSi65/HqoDrENwl6F8Jno7d4t1ZjF
U50P6YthoGrqSRxeA88AOAs0sjI52UcdRc68NGJqKr1p919p6jsSNkeHVCq6Mq6x
YQKBgQDmxKzwJQKBgQDOxKzwJQKBgQDOxKzwJQKBgQDOxKzwJQKBgQDOxKzw
-----END RSA PRIVATE KEY-----`

    // 签名验证公钥 (新的独立密钥)
    V23_SIGNING_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`
)
```

### **步骤4: 更新验证逻辑**

#### **新的V23许可证数据结构**
```go
type V23LicenseData struct {
    CompanyName        string `json:"company_name"`
    Email              string `json:"email"`
    Phone              string `json:"phone"`
    AuthorizedSoftware string `json:"authorized_software"`
    AuthorizedVersion  string `json:"authorized_version"`
    LicenseType        string `json:"license_type"`        // 新字段
    StartDate          string `json:"start_date"`          // 新字段
    ExpirationDate     string `json:"expiration_date"`
    IssuedDate         string `json:"issued_date"`
    EncryptedMachineID string `json:"encrypted_machine_id"`
    Signature          string `json:"signature"`
}
```

#### **更新验证函数调用**
```go
// 旧的调用方式
err := ValidateLicenseFile("license.json")

// 新的调用方式
err := ValidateV23LicenseFile("license.json")
```

### **步骤5: 集成到您的软件**

#### **基本集成示例**
```go
package main

import (
    "fmt"
    "log"
    "os"
)

func main() {
    fmt.Println("🚀 Starting your authorized software...")

    // 验证V23许可证
    err := ValidateV23LicenseFile("license.json")
    if err != nil {
        log.Printf("❌ License validation failed: %v", err)
        fmt.Println("Software cannot start. Please check your license file.")
        os.Exit(1)
    }

    fmt.Println("✅ License validation successful")
    fmt.Println("🎉 Software is authorized, starting...")

    // 继续您的软件逻辑
    startYourApplication()
}

func startYourApplication() {
    // 您的应用程序逻辑
    fmt.Println("Application is running...")
}
```

#### **高级集成示例**
```go
func validateLicenseWithDetails() error {
    // 创建验证器
    validator, err := NewV23LicenseValidator()
    if err != nil {
        return fmt.Errorf("failed to create validator: %v", err)
    }

    // 加载许可证
    license, err := LoadV23LicenseFromFile("license.json")
    if err != nil {
        return fmt.Errorf("failed to load license: %v", err)
    }

    // 显示许可证信息
    fmt.Printf("\n📋 License Information:\n")
    fmt.Printf("  Company: %s\n", license.CompanyName)
    fmt.Printf("  Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
    fmt.Printf("  Type: %s\n", license.LicenseType)
    fmt.Printf("  Active Period: %s to %s\n", license.StartDate, license.ExpirationDate)

    // 验证许可证
    err = validator.ValidateV23License(license)
    if err != nil {
        return fmt.Errorf("license validation failed: %v", err)
    }

    return nil
}
```

## 🧪 **测试验证**

### **测试步骤**
1. **编译新验证器**
   ```bash
   go build -o your_software_v23 main.go V23_LICENSE_VALIDATOR.go
   ```

2. **使用V25生成的测试许可证**
   ```json
   {
     "company_name": "Test Company",
     "email": "<EMAIL>",
     "phone": "************",
     "authorized_software": "Your Software",
     "authorized_version": "1.0.0",
     "license_type": "lease",
     "start_date": "2025-07-12",
     "expiration_date": "2026-07-12",
     "issued_date": "2025-07-12",
     "encrypted_machine_id": "...",
     "signature": "..."
   }
   ```

3. **运行测试**
   ```bash
   ./your_software_v23
   ```

4. **预期结果**
   ```
   ✅ License validation successful
   🎉 Software is authorized, starting...
   ```

## 🔍 **验证更新成功**

### **检查清单**
- [ ] 新验证器可以验证V25生成的许可证
- [ ] 机器绑定验证正常工作
- [ ] 签名验证使用新的公钥
- [ ] 支持新的license_type和start_date字段
- [ ] 错误处理和用户反馈正常

### **验证命令**
```bash
# 检查密钥是否分离
echo "Testing key separation..."

# 应该显示不同的密钥指纹
echo "Machine key: $(head -2 V23_PRIVATE_KEY | tail -1 | cut -c1-20)"
echo "Signature key: $(head -2 V23_SIGNING_PUBLIC_KEY | tail -1 | cut -c1-20)"
```

## 🚨 **故障排除**

### **常见问题**

#### **问题1: "signature verification failed"**
```
原因: 仍在使用旧的签名公钥
解决: 确认使用V23_SIGNING_PUBLIC_KEY而不是旧的EMBEDDED_PUBLIC_KEY
```

#### **问题2: "machine binding validation failed"**
```
原因: 机器ID解密密钥不正确
解决: 确认使用V23_PRIVATE_KEY (与之前相同的密钥)
```

#### **问题3: "required field missing"**
```
原因: 许可证缺少license_type或start_date字段
解决: 确保使用V25生成器创建的许可证
```

### **调试模式**
```go
func debugValidation() {
    fmt.Println("🔍 Debug: License validation details...")
    
    // 加载许可证
    license, err := LoadV23LicenseFromFile("license.json")
    if err != nil {
        fmt.Printf("❌ Failed to load license: %v\n", err)
        return
    }
    
    // 检查必需字段
    fmt.Printf("License Type: %s\n", license.LicenseType)
    fmt.Printf("Start Date: %s\n", license.StartDate)
    fmt.Printf("Signature Length: %d\n", len(license.Signature))
    
    // 逐步验证
    validator, _ := NewV23LicenseValidator()
    err = validator.ValidateV23License(license)
    if err != nil {
        fmt.Printf("❌ Validation failed: %v\n", err)
    } else {
        fmt.Println("✅ Validation successful!")
    }
}
```

## 📞 **技术支持**

### **如果遇到问题**
1. **检查许可证格式**: 确保包含所有V23字段
2. **验证密钥配置**: 确认使用正确的公钥
3. **测试机器绑定**: 确认在正确的机器上运行
4. **查看错误日志**: 分析具体的验证失败原因

### **联系信息**
如果更新过程中遇到问题，请提供：
- 错误信息的完整日志
- 使用的许可证文件格式
- 当前的验证器版本信息

## 🎉 **更新完成确认**

更新成功后，您的软件将能够：
- ✅ 验证V25生成器创建的许可证
- ✅ 支持新的许可证类型 (lease/demo/perpetual)
- ✅ 支持开始日期验证
- ✅ 使用分离的密钥架构提升安全性
- ✅ 保持与现有机器绑定的兼容性

**恭喜！您的软件现在支持最新的安全许可证架构！** 🔐✨
