# Machine ID Implementation Guide
# 机器ID实现指南

## ⚠️ CRITICAL REQUIREMENT / 关键要求

**The `getCurrentMachineID()` function in `standalone_license_validator.go` MUST return the SAME machine ID that was used when generating the license.**

**`standalone_license_validator.go` 中的 `getCurrentMachineID()` 函数必须返回与生成许可证时使用的相同机器ID。**

## 🔍 Current Implementation / 当前实现

The current placeholder implementation in `standalone_license_validator.go`:

```go
func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
    // TODO: 实现获取当前机器ID的逻辑
    // 这里应该使用与生成机器信息文件时相同的方法
    
    // 示例实现（需要根据实际情况修改）
    machineID := fmt.Sprintf("%s-%s-%s", 
        "711221f2-c02b-4058-b6ac-165578baae25", // CPU ID
        "S9U0BB2481000104",                     // 硬盘序列号
        runtime.GOOS)                           // 操作系统
    
    return machineID, nil
}
```

## 🎯 Required Implementation / 需要的实现

Based on the factory machine info file, the machine ID format appears to be:
根据工厂机器信息文件，机器ID格式似乎是：

```
Format: {UUID}-{DISK_SERIAL}
Example: 711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104
```

## 💻 Platform-Specific Implementations / 平台特定实现

### Windows Implementation / Windows实现

```go
package main

import (
    "fmt"
    "os/exec"
    "strings"
    "syscall"
    "unsafe"
)

func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
    // Get CPU UUID
    cpuID, err := getWindowsCPUID()
    if err != nil {
        return "", fmt.Errorf("failed to get CPU ID: %v", err)
    }
    
    // Get disk serial number
    diskSerial, err := getWindowsDiskSerial()
    if err != nil {
        return "", fmt.Errorf("failed to get disk serial: %v", err)
    }
    
    // Combine to match the format used in license generation
    machineID := fmt.Sprintf("%s-%s", cpuID, diskSerial)
    return machineID, nil
}

func getWindowsCPUID() (string, error) {
    // Method 1: Using WMI
    cmd := exec.Command("wmic", "cpu", "get", "ProcessorId", "/value")
    output, err := cmd.Output()
    if err != nil {
        return "", err
    }
    
    lines := strings.Split(string(output), "\n")
    for _, line := range lines {
        if strings.HasPrefix(line, "ProcessorId=") {
            return strings.TrimSpace(strings.Split(line, "=")[1]), nil
        }
    }
    
    return "", fmt.Errorf("CPU ID not found")
}

func getWindowsDiskSerial() (string, error) {
    // Method 1: Using WMI for system drive
    cmd := exec.Command("wmic", "diskdrive", "where", "index=0", "get", "SerialNumber", "/value")
    output, err := cmd.Output()
    if err != nil {
        return "", err
    }
    
    lines := strings.Split(string(output), "\n")
    for _, line := range lines {
        if strings.HasPrefix(line, "SerialNumber=") {
            serial := strings.TrimSpace(strings.Split(line, "=")[1])
            return strings.ReplaceAll(serial, " ", ""), nil
        }
    }
    
    return "", fmt.Errorf("disk serial not found")
}
```

### Linux Implementation / Linux实现

```go
func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
    // Get CPU UUID
    cpuID, err := getLinuxCPUID()
    if err != nil {
        return "", fmt.Errorf("failed to get CPU ID: %v", err)
    }
    
    // Get disk serial number
    diskSerial, err := getLinuxDiskSerial()
    if err != nil {
        return "", fmt.Errorf("failed to get disk serial: %v", err)
    }
    
    machineID := fmt.Sprintf("%s-%s", cpuID, diskSerial)
    return machineID, nil
}

func getLinuxCPUID() (string, error) {
    // Read from /proc/cpuinfo
    data, err := ioutil.ReadFile("/proc/cpuinfo")
    if err != nil {
        return "", err
    }
    
    lines := strings.Split(string(data), "\n")
    for _, line := range lines {
        if strings.Contains(line, "processor") && strings.Contains(line, "0") {
            // Extract processor ID or use machine-id
            break
        }
    }
    
    // Alternative: use machine-id
    machineID, err := ioutil.ReadFile("/etc/machine-id")
    if err != nil {
        return "", err
    }
    
    return strings.TrimSpace(string(machineID)), nil
}

func getLinuxDiskSerial() (string, error) {
    // Use lsblk or hdparm to get disk serial
    cmd := exec.Command("lsblk", "-o", "SERIAL", "-n")
    output, err := cmd.Output()
    if err != nil {
        return "", err
    }
    
    lines := strings.Split(string(output), "\n")
    for _, line := range lines {
        line = strings.TrimSpace(line)
        if line != "" && line != "SERIAL" {
            return line, nil
        }
    }
    
    return "", fmt.Errorf("disk serial not found")
}
```

### Cross-Platform Implementation / 跨平台实现

```go
import (
    "runtime"
)

func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
    switch runtime.GOOS {
    case "windows":
        return lv.getWindowsMachineID()
    case "linux":
        return lv.getLinuxMachineID()
    case "darwin":
        return lv.getMacOSMachineID()
    default:
        return "", fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
    }
}
```

## 🧪 Testing Your Implementation / 测试您的实现

### Test Function / 测试函数

```go
func TestMachineIDConsistency() {
    validator, err := NewLicenseValidator()
    if err != nil {
        log.Fatal("Failed to create validator:", err)
    }
    
    // Get machine ID multiple times
    for i := 0; i < 5; i++ {
        machineID, err := validator.getCurrentMachineID()
        if err != nil {
            log.Fatal("Failed to get machine ID:", err)
        }
        fmt.Printf("Attempt %d: %s\n", i+1, machineID)
    }
    
    fmt.Println("✅ Machine ID should be consistent across all attempts")
}
```

### Validation Test / 验证测试

```go
func TestLicenseValidationWithRealMachineID() {
    // This test should pass if your machine ID implementation is correct
    err := ValidateLicenseFile("factory_license.json")
    if err != nil {
        if strings.Contains(err.Error(), "machine binding") {
            fmt.Println("❌ Machine ID mismatch detected!")
            fmt.Println("   Your getCurrentMachineID() implementation may be incorrect")
            fmt.Println("   Expected format: {CPU_UUID}-{DISK_SERIAL}")
        } else {
            fmt.Printf("❌ Other validation error: %v\n", err)
        }
    } else {
        fmt.Println("✅ License validation successful!")
        fmt.Println("   Your machine ID implementation is correct")
    }
}
```

## 🔧 Debugging Machine ID Issues / 调试机器ID问题

### Debug Function / 调试函数

```go
func DebugMachineID() {
    fmt.Println("=== Machine ID Debug Information ===")
    
    validator, err := NewLicenseValidator()
    if err != nil {
        fmt.Printf("Error creating validator: %v\n", err)
        return
    }
    
    // Get current machine ID
    currentID, err := validator.getCurrentMachineID()
    if err != nil {
        fmt.Printf("Error getting machine ID: %v\n", err)
        return
    }
    
    fmt.Printf("Current Machine ID: %s\n", currentID)
    fmt.Printf("Length: %d characters\n", len(currentID))
    
    // Load license and compare
    license, err := LoadLicenseFromFile("factory_license.json")
    if err != nil {
        fmt.Printf("Error loading license: %v\n", err)
        return
    }
    
    // Decrypt license machine ID
    decryptedID, err := validator.decryptMachineID(license.EncryptedMachineID)
    if err != nil {
        fmt.Printf("Error decrypting license machine ID: %v\n", err)
        return
    }
    
    fmt.Printf("License Machine ID: %s\n", decryptedID)
    fmt.Printf("Length: %d characters\n", len(decryptedID))
    
    if currentID == decryptedID {
        fmt.Println("✅ Machine IDs match!")
    } else {
        fmt.Println("❌ Machine IDs do NOT match!")
        fmt.Println("   This will cause license validation to fail")
    }
}
```

## 📝 Implementation Checklist / 实现检查清单

- [ ] Identify the exact machine ID format used in your license generation
- [ ] Implement platform-specific machine ID collection
- [ ] Ensure consistency across multiple calls
- [ ] Test with actual license files
- [ ] Handle errors gracefully
- [ ] Consider fallback methods for edge cases

- [ ] 确定许可证生成中使用的确切机器ID格式
- [ ] 实现平台特定的机器ID收集
- [ ] 确保多次调用的一致性
- [ ] 使用实际许可证文件进行测试
- [ ] 优雅地处理错误
- [ ] 考虑边缘情况的备用方法

## 🆘 Common Issues / 常见问题

1. **Inconsistent Machine ID**: Different values returned on each call
2. **Format Mismatch**: Machine ID format doesn't match license generation
3. **Permission Issues**: Insufficient privileges to access hardware information
4. **Platform Differences**: Different behavior on different operating systems

## 📞 Support / 支持

If you cannot determine the correct machine ID format:

1. Contact the license provider for the exact machine ID generation algorithm
2. Provide sample machine information from your target systems
3. Request a test license for validation

如果您无法确定正确的机器ID格式：

1. 联系许可证提供商获取确切的机器ID生成算法
2. 提供目标系统的示例机器信息
3. 请求测试许可证进行验证
