package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	CompanyRegistryFile = "company_registry.json"
	CompanyIDStartRange = 1000000 // Start from 1000000 to ensure 7 digits
)

// LoadCompanyRegistry loads the company registry from file
func LoadCompanyRegistry() (*CompanyRegistry, error) {
	// Check if file exists
	if _, err := os.Stat(CompanyRegistryFile); os.IsNotExist(err) {
		// Create new registry if file doesn't exist
		registry := &CompanyRegistry{
			Version:         "1.0",
			LastUpdated:     time.Now().Format("2006-01-02T15:04:05Z"),
			NextAvailableID: CompanyIDStartRange,
			Companies:       []CompanyRecord{},
		}
		return registry, nil
	}

	// Read existing file
	data, err := ioutil.ReadFile(CompanyRegistryFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read company registry file: %v", err)
	}

	var registry CompanyRegistry
	if err := json.Unmarshal(data, &registry); err != nil {
		return nil, fmt.Errorf("failed to parse company registry: %v", err)
	}

	return &registry, nil
}

// SaveCompanyRegistry saves the company registry to file
func SaveCompanyRegistry(registry *CompanyRegistry) error {
	registry.LastUpdated = time.Now().Format("2006-01-02T15:04:05Z")

	data, err := json.MarshalIndent(registry, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal company registry: %v", err)
	}

	if err := ioutil.WriteFile(CompanyRegistryFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write company registry file: %v", err)
	}

	return nil
}

// FormatCompanyID formats a numeric company ID to the display format (123-4567)
func FormatCompanyID(id int) string {
	idStr := fmt.Sprintf("%07d", id) // Ensure 7 digits with leading zeros
	return fmt.Sprintf("%s-%s", idStr[:3], idStr[3:])
}

// ParseCompanyID parses a formatted company ID (123-4567) to numeric value
func ParseCompanyID(formattedID string) (int, error) {
	// Remove any formatting
	cleaned := strings.ReplaceAll(formattedID, "-", "")
	cleaned = strings.ReplaceAll(cleaned, " ", "")
	
	if len(cleaned) != 7 {
		return 0, fmt.Errorf("company ID must be exactly 7 digits")
	}

	id, err := strconv.Atoi(cleaned)
	if err != nil {
		return 0, fmt.Errorf("company ID must contain only digits")
	}

	return id, nil
}

// ValidateCompanyID validates the format and uniqueness of a company ID
func ValidateCompanyID(formattedID string, registry *CompanyRegistry) error {
	// Parse and validate format
	_, err := ParseCompanyID(formattedID)
	if err != nil {
		return err
	}

	// Check for uniqueness
	for _, company := range registry.Companies {
		if company.CompanyID == formattedID {
			return fmt.Errorf("company ID %s already exists for company: %s", formattedID, company.CompanyName)
		}
	}

	return nil
}

// GetNextAvailableCompanyID generates the next available company ID
func GetNextAvailableCompanyID(registry *CompanyRegistry) string {
	id := registry.NextAvailableID
	registry.NextAvailableID++
	return FormatCompanyID(id)
}

// FindCompanyByName searches for a company by name (case-insensitive)
func FindCompanyByName(companyName string, registry *CompanyRegistry) *CompanyRecord {
	lowerName := strings.ToLower(companyName)
	for i, company := range registry.Companies {
		if strings.ToLower(company.CompanyName) == lowerName {
			return &registry.Companies[i]
		}
	}
	return nil
}

// FindCompanyByID searches for a company by ID
func FindCompanyByID(companyID string, registry *CompanyRegistry) *CompanyRecord {
	for i, company := range registry.Companies {
		if company.CompanyID == companyID {
			return &registry.Companies[i]
		}
	}
	return nil
}

// AddOrUpdateCompany adds a new company or updates an existing one
func AddOrUpdateCompany(companyID, companyName, email, phone string, registry *CompanyRegistry) error {
	// Validate company ID format
	if err := ValidateCompanyID(companyID, registry); err != nil {
		// Check if it's an update (ID already exists)
		existing := FindCompanyByID(companyID, registry)
		if existing == nil {
			return err // New ID with validation error
		}
		// Update existing record
		existing.CompanyName = companyName
		existing.Email = email
		existing.Phone = phone
		existing.LastUsed = time.Now().Format("2006-01-02")
		return nil
	}

	// Add new company record
	newRecord := CompanyRecord{
		CompanyID:   companyID,
		CompanyName: companyName,
		Email:       email,
		Phone:       phone,
		CreatedDate: time.Now().Format("2006-01-02"),
		LastUsed:    time.Now().Format("2006-01-02"),
		Notes:       "",
	}

	registry.Companies = append(registry.Companies, newRecord)

	// Sort companies by ID for better organization
	sort.Slice(registry.Companies, func(i, j int) bool {
		return registry.Companies[i].CompanyID < registry.Companies[j].CompanyID
	})

	return nil
}

// GetCompanySuggestions returns a list of company names for auto-completion
func GetCompanySuggestions(registry *CompanyRegistry) []string {
	suggestions := make([]string, len(registry.Companies))
	for i, company := range registry.Companies {
		suggestions[i] = fmt.Sprintf("%s (%s)", company.CompanyName, company.CompanyID)
	}
	return suggestions
}

// UpdateCompanyLastUsed updates the last used date for a company
func UpdateCompanyLastUsed(companyID string, registry *CompanyRegistry) {
	if company := FindCompanyByID(companyID, registry); company != nil {
		company.LastUsed = time.Now().Format("2006-01-02")
	}
}

// GetOrCreateCompanyID gets an existing company ID or creates a new one
func GetOrCreateCompanyID(companyName, email, phone string, registry *CompanyRegistry) (string, error) {
	// First, try to find existing company by name
	if existing := FindCompanyByName(companyName, registry); existing != nil {
		// Update last used date
		UpdateCompanyLastUsed(existing.CompanyID, registry)
		return existing.CompanyID, nil
	}

	// Create new company ID
	newID := GetNextAvailableCompanyID(registry)
	if err := AddOrUpdateCompany(newID, companyName, email, phone, registry); err != nil {
		return "", err
	}

	return newID, nil
}
