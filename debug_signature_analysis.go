// 详细分析当前license的签名问题
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 可能的旧版本签名数据结构
type OldSignatureData1 struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationUnix     int64  `json:"expiration_unix"`
	MachineIDHash      string `json:"machine_id_hash"`
}

type OldSignatureData2 struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"` // 可能包含完整的软件名称+版本
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}
	
	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}
	
	return privateKey, nil
}

func testSignatureVariant(license *LicenseData, decryptedMachineID string, privateKey *rsa.PrivateKey, variant string, sigData interface{}) bool {
	fmt.Printf("\n🧪 Testing Variant: %s\n", variant)
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		fmt.Printf("❌ Failed to marshal: %v\n", err)
		return false
	}
	
	fmt.Printf("📋 JSON: %s\n", string(jsonData))
	
	hash := sha256.Sum256(jsonData)
	
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		fmt.Printf("❌ Failed to decode signature: %v\n", err)
		return false
	}
	
	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ Verification failed: %v\n", err)
		return false
	}
	
	fmt.Printf("✅ Verification successful!\n")
	return true
}

func main() {
	fmt.Println("🔍 Detailed Signature Analysis")
	fmt.Println("==============================")
	
	// 1. 加载license
	licenseData, err := os.ReadFile("factory_license.json")
	if err != nil {
		fmt.Printf("❌ Failed to read license: %v\n", err)
		return
	}
	
	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		fmt.Printf("❌ Failed to parse license: %v\n", err)
		return
	}
	
	fmt.Printf("📋 License Info:\n")
	fmt.Printf("  Company: %s\n", license.CompanyName)
	fmt.Printf("  Email: %s\n", license.Email)
	fmt.Printf("  Software: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  Version: %s\n", license.AuthorizedVersion)
	fmt.Printf("  Expiration: %s\n", license.ExpirationDate)
	
	// 2. 加载私钥
	privateKey, err := loadPrivateKey()
	if err != nil {
		fmt.Printf("❌ Failed to load private key: %v\n", err)
		return
	}
	
	// 3. 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		fmt.Printf("❌ Failed to decode encrypted machine ID: %v\n", err)
		return
	}
	
	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		fmt.Printf("❌ Failed to decrypt machine ID: %v\n", err)
		return
	}
	
	fmt.Printf("🔓 Decrypted Machine ID: %s\n", string(decryptedMachineID))
	
	// 4. 解析过期时间
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		fmt.Printf("❌ Failed to parse expiration date: %v\n", err)
		return
	}
	
	fmt.Printf("📅 Expiration Unix: %d\n", expirationTime.Unix())
	fmt.Printf("🔑 Machine ID Hash: %s\n", hashString(string(decryptedMachineID)))
	
	// 5. 测试不同的签名数据变体
	fmt.Println("\n🧪 Testing Different Signature Data Variants...")
	
	// 变体1: 当前标准格式
	variant1 := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	if testSignatureVariant(&license, string(decryptedMachineID), privateKey, "Current Standard", variant1) {
		fmt.Println("\n🎉 Found working variant: Current Standard")
		return
	}
	
	// 变体2: 旧版本格式1
	variant2 := OldSignatureData1{
		CompanyName:        license.CompanyName,
		Email:              license.Email,
		AuthorizedSoftware: license.AuthorizedSoftware,
		AuthorizedVersion:  license.AuthorizedVersion,
		ExpirationUnix:     expirationTime.Unix(),
		MachineIDHash:      hashString(string(decryptedMachineID)),
	}
	
	if testSignatureVariant(&license, string(decryptedMachineID), privateKey, "Old Format 1", variant2) {
		fmt.Println("\n🎉 Found working variant: Old Format 1")
		return
	}
	
	// 变体3: 软件名称包含版本
	variant3 := OldSignatureData2{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware + " v" + license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	if testSignatureVariant(&license, string(decryptedMachineID), privateKey, "Software with Version", variant3) {
		fmt.Println("\n🎉 Found working variant: Software with Version")
		return
	}
	
	// 变体4: 不分离版本的格式
	variant4 := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware + " v" + license.AuthorizedVersion,
		Version:        "",
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	if testSignatureVariant(&license, string(decryptedMachineID), privateKey, "Combined Software+Version", variant4) {
		fmt.Println("\n🎉 Found working variant: Combined Software+Version")
		return
	}
	
	// 变体5: 使用原始机器ID而不是哈希
	variant5 := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  string(decryptedMachineID), // 使用原始ID而不是哈希
	}
	
	if testSignatureVariant(&license, string(decryptedMachineID), privateKey, "Raw Machine ID", variant5) {
		fmt.Println("\n🎉 Found working variant: Raw Machine ID")
		return
	}
	
	fmt.Println("\n❌ No working signature variant found!")
	fmt.Println("The license may have been generated with a different algorithm or key.")
}
