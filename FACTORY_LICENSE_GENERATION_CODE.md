# Factory License Generation - Complete Code Reference
# 工厂许可证生成 - 完整代码参考

## 📋 Overview / 概述

This document contains all the code related to generating `factory_license.json` files for authorized software integration and analysis.

本文档包含生成`factory_license.json`文件的所有相关代码，供被授权软件集成和分析。

## 🏗️ Core Data Structures / 核心数据结构

### 1. License Data Structure / 许可证数据结构
```go
// File: models.go
package main

import "time"

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// MachineInfo represents machine information from JSON file
type MachineInfo struct {
	CompanyName string `json:"CompanyName"`
	Email       string `json:"Email"`
	Phone       string `json:"Phone"`
	GeneratedBy string `json:"GeneratedBy"`
	MachineID   string `json:"MachineID"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

// ExpirationPreset represents a predefined expiration option
type ExpirationPreset struct {
	Label string
	Days  int
}
```

### 2. License Generator Structure / 许可证生成器结构
```go
// File: license.go
package main

import (
	"crypto/rsa"
	"fmt"
	"time"
)

// LicenseGenerator handles license generation
type LicenseGenerator struct {
	privateKey  *rsa.PrivateKey
	machineInfo *MachineInfo
}

// NewLicenseGenerator creates a new license generator
func NewLicenseGenerator(machineInfoPath, privateKeyPath string) (*LicenseGenerator, error) {
	// Load private key
	privateKey, err := LoadPrivateKey(privateKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load private key: %v", err)
	}

	// Load machine info
	machineInfo, err := LoadMachineInfo(machineInfoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load machine info: %v", err)
	}

	return &LicenseGenerator{
		privateKey:  privateKey,
		machineInfo: machineInfo,
	}, nil
}
```

## 🔐 Cryptographic Functions / 加密函数

### 1. Key Loading Functions / 密钥加载函数
```go
// File: crypto.go
package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LoadPrivateKey loads an RSA private key from a PEM file
func LoadPrivateKey(filePath string) (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read private key file: %v", err)
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return privateKey, nil
}

// LoadMachineInfo loads machine information from JSON file
func LoadMachineInfo(filePath string) (*MachineInfo, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read machine info file: %v", err)
	}

	var machineInfo MachineInfo
	err = json.Unmarshal(data, &machineInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to parse machine info JSON: %v", err)
	}

	return &machineInfo, nil
}
```

### 2. Encryption Functions / 加密函数
```go
// EncryptMachineID encrypts the machine ID using RSA public key
func EncryptMachineID(machineID string, privateKey *rsa.PrivateKey) (string, error) {
	publicKey := &privateKey.PublicKey
	
	encryptedData, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, []byte(machineID), nil)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt machine ID: %v", err)
	}

	return base64.StdEncoding.EncodeToString(encryptedData), nil
}

// CreateSignature creates a digital signature for the license data
func CreateSignature(licenseData *LicenseData, machineID string, privateKey *rsa.PrivateKey) (string, error) {
	// Parse expiration date
	expirationTime, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
	if err != nil {
		return "", fmt.Errorf("failed to parse expiration date: %v", err)
	}

	// Create signature data with shortened keys for compactness
	sigData := SignatureData{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(machineID),
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// Create hash
	hash := sha256.Sum256(jsonData)

	// Sign the hash
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to sign data: %v", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
```

### 3. File Operations / 文件操作
```go
// SaveLicenseToFile saves the license data to a JSON file
func SaveLicenseToFile(license *LicenseData, filePath string) error {
	jsonData, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal license data: %v", err)
	}

	// Check if file exists before writing
	if _, err := os.Stat(filePath); err == nil {
		// File exists, remove it first to ensure clean write
		err = os.Remove(filePath)
		if err != nil {
			return fmt.Errorf("failed to remove existing file: %v", err)
		}
	}

	err = os.WriteFile(filePath, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("failed to write license file: %v", err)
	}

	// Verify the file was written correctly
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file was not created successfully: %s", filePath)
	}

	return nil
}
```

## 🏭 License Generation Logic / 许可证生成逻辑

### 1. Main Generation Function / 主生成函数
```go
// File: license.go

// GenerateLicense generates a complete license with all security features
func (lg *LicenseGenerator) GenerateLicense(companyName, email, phone, software, version string, expirationDate time.Time) (*LicenseData, error) {
	// Create license data structure
	license := &LicenseData{
		CompanyName:        companyName,
		Email:              email,
		Phone:              phone,
		AuthorizedSoftware: software,
		AuthorizedVersion:  version,
		ExpirationDate:     expirationDate.Format("2006-01-02"),
		IssuedDate:         time.Now().Format("2006-01-02"),
	}

	// Encrypt machine ID
	encryptedMachineID, err := EncryptMachineID(lg.machineInfo.MachineID, lg.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt machine ID: %v", err)
	}
	license.EncryptedMachineID = encryptedMachineID

	// Create digital signature
	signature, err := CreateSignature(license, lg.machineInfo.MachineID, lg.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create signature: %v", err)
	}
	license.Signature = signature

	return license, nil
}
```

### 2. Software Name and Version Extraction / 软件名称和版本提取
```go
// File: main.go

// extractSoftwareAndVersion extracts software name and version from GeneratedBy field
func extractSoftwareAndVersion(generatedBy string) (string, string) {
	// Look for version pattern like "v1.2.3" or "V1.2.3"
	parts := strings.Fields(generatedBy)
	
	var softwareParts []string
	var version string
	
	for _, part := range parts {
		// Check if this part looks like a version (starts with v/V followed by digits)
		if len(part) > 1 && (strings.ToLower(part[:1]) == "v") {
			// Check if the rest contains digits and dots
			versionPart := part[1:]
			if containsVersionPattern(versionPart) {
				version = versionPart
				break
			}
		}
		softwareParts = append(softwareParts, part)
	}
	
	softwareName := strings.Join(softwareParts, " ")
	
	// If no version found, return the entire string as software name
	if version == "" {
		return generatedBy, ""
	}
	
	return softwareName, version
}

// containsVersionPattern checks if a string contains version-like pattern
func containsVersionPattern(s string) bool {
	// Simple check for digits and dots pattern
	hasDigit := false
	for _, char := range s {
		if char >= '0' && char <= '9' {
			hasDigit = true
		} else if char != '.' && char != '-' && char != '_' {
			return false
		}
	}
	return hasDigit
}
```

## 📅 Date and Expiration Handling / 日期和过期处理

### 1. Expiration Presets / 过期预设
```go
// File: main.go

// GetExpirationPresets returns predefined expiration options
func GetExpirationPresets() []ExpirationPreset {
	return []ExpirationPreset{
		{"1 Month", 30},
		{"3 Months", 90},
		{"6 Months", 180},
		{"1 Year", 365},
		{"2 Years", 730},
		{"5 Years", 1825},
		{"Custom", 0},
	}
}

// CalculateExpirationDate calculates expiration date from preset
func CalculateExpirationDate(preset ExpirationPreset) time.Time {
	if preset.Days == 0 {
		// Custom option, return 1 year from now as default
		return time.Now().AddDate(1, 0, 0)
	}
	return time.Now().AddDate(0, 0, preset.Days)
}
```

## 🎨 Display Formatting / 显示格式化

### 1. License Display Function / 许可证显示函数
```go
// File: main.go

// FormatLicenseForDisplay formats license data for GUI display
func FormatLicenseForDisplay(license *LicenseData) string {
	return fmt.Sprintf(`Generated License:

Company: %s
Email: %s
Phone: %s
Authorized Software: %s
Authorized Version: %s
Expiration Date: %s
Issued Date: %s

Encrypted Machine ID: %s...
Digital Signature: %s...

License file ready to save.`,
		license.CompanyName,
		license.Email,
		license.Phone,
		license.AuthorizedSoftware,
		license.AuthorizedVersion,
		license.ExpirationDate,
		license.IssuedDate,
		license.EncryptedMachineID[:50],
		license.Signature[:50])
}
```

## 🔑 Embedded Keys for Reference / 嵌入密钥参考

### RSA Key Pair Used / 使用的RSA密钥对
```go
// These are the actual keys used in the system
// 这些是系统中使用的实际密钥

const RSA_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

const RSA_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

## 📄 Example Usage / 使用示例

### Complete License Generation Example / 完整许可证生成示例
```go
package main

import (
	"fmt"
	"log"
	"time"
)

func main() {
	// Example: Generate a factory license

	// 1. Create license generator
	generator, err := NewLicenseGenerator(
		"factory_machine_info.json",
		"machine_decryption_private_key_to_decryp_factory_machineinfo.pem",
	)
	if err != nil {
		log.Fatal("Failed to create generator:", err)
	}

	// 2. Set license parameters
	companyName := "Li auto2"
	email := "<EMAIL>"
	phone := "18101928290"
	software := "LS-DYNA Model License Generate Factory"
	version := "2.3.0"
	expirationDate := time.Now().AddDate(0, 6, 0) // 6 months from now

	// 3. Generate license
	license, err := generator.GenerateLicense(
		companyName,
		email,
		phone,
		software,
		version,
		expirationDate,
	)
	if err != nil {
		log.Fatal("Failed to generate license:", err)
	}

	// 4. Save to file
	err = SaveLicenseToFile(license, "factory_license.json")
	if err != nil {
		log.Fatal("Failed to save license:", err)
	}

	fmt.Println("License generated successfully!")
	fmt.Printf("Company: %s\n", license.CompanyName)
	fmt.Printf("Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("Expires: %s\n", license.ExpirationDate)
}
```

## 📋 Generated License File Format / 生成的许可证文件格式

### Example factory_license.json / 示例factory_license.json
```json
{
  "company_name": "Li auto2",
  "email": "<EMAIL>",
  "phone": "18101928290",
  "authorized_software": "LS-DYNA Model License Generate Factory",
  "authorized_version": "2.3.0",
  "expiration_date": "2025-08-10",
  "issued_date": "2025-07-09",
  "encrypted_machine_id": "GXdvrGcNO4Vj73bZ/3qbxrI8ZQMfDnAwX62P3Um/T/mmsrPOg1...",
  "signature": "h0/mo+4yQupr7QmbZCr7w80c4JwFSMvxFp7FlqsUTUaF0s59PYAw0j63yb26OLUi..."
}
```

## 🔍 Signature Data Construction Details / 签名数据构建详情

### Internal Signature JSON / 内部签名JSON
```json
{
  "c": "Li auto2",
  "e": "<EMAIL>",
  "s": "LS-DYNA Model License Generate Factory",
  "v": "2.3.0",
  "x": **********,
  "m": "jKl9mN2pQ3rS"
}
```

### Signature Construction Process / 签名构建过程
1. **Create SignatureData** with shortened keys
2. **JSON Marshal** the signature data
3. **SHA256 Hash** the JSON bytes
4. **RSA Sign** the hash with PKCS1v15
5. **Base64 Encode** the signature

## 🛠️ Integration Requirements / 集成要求

### For Authorized Software Developers / 被授权软件开发者

#### Required Functions / 必需函数
1. **LoadPrivateKey()** - Load RSA private key from PEM
2. **LoadMachineInfo()** - Load machine info from JSON
3. **EncryptMachineID()** - Encrypt machine ID with RSA
4. **CreateSignature()** - Create digital signature
5. **SaveLicenseToFile()** - Save license to JSON file

#### Required Dependencies / 必需依赖
```go
import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)
```

#### Key Security Considerations / 关键安全考虑
1. **Private Key Protection** - Keep RSA private key secure
2. **Machine ID Binding** - Ensure machine ID is unique and consistent
3. **Signature Verification** - Validate all signatures before use
4. **Date Validation** - Check expiration dates properly

## 📚 Related Files / 相关文件

### Core Implementation Files / 核心实现文件
- `models.go` - Data structures
- `crypto.go` - Cryptographic functions
- `license.go` - License generation logic
- `main.go` - GUI and main application logic

### Key Files / 密钥文件
- `machine_decryption_private_key_to_decryp_factory_machineinfo.pem` - RSA private key
- `public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem` - RSA public key

### Data Files / 数据文件
- `factory_machine_info.json` - Machine information input
- `factory_license.json` - Generated license output

### Documentation / 文档
- `standalone_license_validator.go` - Complete validation reference
- `LICENSE_VALIDATOR_INTEGRATION_GUIDE.md` - Integration guide
- `SIGNATURE_DATA_CONSTRUCTION_GUIDE.md` - Signature details

---

**This complete code reference provides everything needed to understand and integrate factory license generation.**

**此完整代码参考提供了理解和集成工厂许可证生成所需的一切。**
```
