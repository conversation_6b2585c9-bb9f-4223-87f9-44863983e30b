// generate_signing_keys.go
// 生成独立的数字签名密钥对

package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔐 Generating Separate Signing Key Pair...")
	fmt.Println("=========================================")

	// 生成RSA密钥对 (2048位)
	fmt.Println("📝 Generating RSA-2048 key pair...")
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Printf("❌ Failed to generate private key: %v\n", err)
		os.Exit(1)
	}

	// 验证密钥
	err = privateKey.Validate()
	if err != nil {
		fmt.Printf("❌ Generated key is invalid: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ RSA key pair generated successfully")

	// 保存私钥
	privateKeyFile := "license_signing_private_key.pem"
	fmt.Printf("💾 Saving private key to: %s\n", privateKeyFile)

	privateKeyPEM := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}

	privateKeyFileHandle, err := os.Create(privateKeyFile)
	if err != nil {
		fmt.Printf("❌ Failed to create private key file: %v\n", err)
		os.Exit(1)
	}
	defer privateKeyFileHandle.Close()

	err = pem.Encode(privateKeyFileHandle, privateKeyPEM)
	if err != nil {
		fmt.Printf("❌ Failed to write private key: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Private key saved to: %s\n", privateKeyFile)

	// 保存公钥
	publicKeyFile := "license_signing_public_key.pem"
	fmt.Printf("💾 Saving public key to: %s\n", publicKeyFile)

	publicKey := &privateKey.PublicKey
	publicKeyPKCS1 := x509.MarshalPKCS1PublicKey(publicKey)

	publicKeyPEM := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyPKCS1,
	}

	publicKeyFileHandle, err := os.Create(publicKeyFile)
	if err != nil {
		fmt.Printf("❌ Failed to create public key file: %v\n", err)
		os.Exit(1)
	}
	defer publicKeyFileHandle.Close()

	err = pem.Encode(publicKeyFileHandle, publicKeyPEM)
	if err != nil {
		fmt.Printf("❌ Failed to write public key: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Public key saved to: %s\n", publicKeyFile)

	// 显示密钥信息
	fmt.Println("\n📋 Key Information:")
	fmt.Println("===================")
	fmt.Printf("🔐 Algorithm: RSA-2048\n")
	fmt.Printf("🔑 Private Key: %s (for license generation)\n", privateKeyFile)
	fmt.Printf("🔓 Public Key: %s (for license validation)\n", publicKeyFile)
	fmt.Printf("📏 Key Size: %d bits\n", privateKey.Size()*8)

	// 显示密钥用途
	fmt.Println("\n🎯 Key Usage:")
	fmt.Println("=============")
	fmt.Println("📝 Private Key Usage:")
	fmt.Println("   - Digital signature creation")
	fmt.Println("   - License data signing")
	fmt.Println("   - Used by License Generator")
	fmt.Println()
	fmt.Println("🔍 Public Key Usage:")
	fmt.Println("   - Digital signature verification")
	fmt.Println("   - License validation")
	fmt.Println("   - Used by License Validator")

	// 安全提醒
	fmt.Println("\n⚠️  Security Reminders:")
	fmt.Println("=======================")
	fmt.Println("🔒 Keep private key secure and confidential")
	fmt.Println("📤 Public key can be distributed freely")
	fmt.Println("🔄 Update validators with new public key")
	fmt.Println("🗂️  Backup keys in secure location")

	fmt.Println("\n🎉 Signing key pair generation completed successfully!")
}
