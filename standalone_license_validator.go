// standalone_license_validator.go
// 这是一个完全独立的license验证器，可以直接复制到被授权软件项目中使用
// 包含所有必要的结构体、常量和函数，无需其他依赖

package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"runtime"
	"time"
)

// ===== 数据结构定义 =====

// LicenseData represents the license information to be validated
// Uses hybrid strategy: public info in plaintext + encrypted machine binding
type LicenseData struct {
	// Public information (plaintext for transparency)
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"` // Software name only
	AuthorizedVersion  string `json:"authorized_version"`  // Software version only
	ExpirationDate     string `json:"expiration_date"`     // Only date in YYYY-MM-DD format
	IssuedDate         string `json:"issued_date"`         // Only date in YYYY-MM-DD format (renamed from generated_date)

	// Machine binding (encrypted MachineID from machine info file)
	// Users can compare this with their machine info file to verify it's for their computer
	EncryptedMachineID string `json:"encrypted_machine_id"` // Direct copy from machine info file

	// Digital signature (for integrity and authenticity)
	Signature string `json:"signature"`
}

// SignatureData represents the data used to create the signature
// This should be compact to fit within RSA2048 size limits
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

// ===== 嵌入的密钥常量 =====

// 需要嵌入到被授权软件中的密钥（从license生成器提取）
const (
	// RSA公钥 - 用于验证license签名
	EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

	// RSA私钥 - 用于解密机器ID绑定验证
	// 注意：这个私钥需要嵌入到被授权软件中，建议使用代码混淆保护
	EMBEDDED_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWs
BI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl
7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfC
n9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85j
j/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrk
Plt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQABAoIBAAGvHsEbie3RgKww
y2yX8TnJWcUoewAVn5zrkdMrsHmDO/szRb3n3EhEgJsYLQBRud7ejbAE3oLgKBe0
5vdmHmQfIOxc1gz9XGREC0oTCC6TgjsIH6S8OUVPBsqNyKZrecalLd/1u3GcO4qq
fuiC3UAHhKG5KEwXNoJlOPiZCp4UP80x3dEC+Fzc5l4Wd5AdcKiQzGg9bxdAmIbj
JEIqSCEOq+m2536zysSi9g7INDRj4yKwVIROSi65/HqoDrENwl6F8Jno7d4t1ZjF
U50P6YthoGrqSRxeA88AOAs0sjI52UcdRc68NGJqKr1p919p6jsSNkeHVCq6Mq6x
PyI8PckCgYEA026DzkLHQwiziaBmjgrcS4SvcTpttveFMhKOvCnB7nQKvz755tHn
n28VOcGsIUhtC+cKJ87fMb4WTS5eVlHBiw/BO5zapGu6Pmp3D+aZa3EO/YFAO9pi
A166NJZcbK/1w7rf2eWmQpyTUf1GMaX/rmo49Phy9gj3qemSX48Hn60CgYEA9+2h
MEP3jfK4BC8jt9BVCJNQRtMJF4FtH8FgWtuTGRuJYsQu4bWvbW7HdMeV+wJDe1cq
QDOhYOxSBPxPxAfqnmxScb7QVww0imqZD8Ibf+vRp7IDxx70oPYNSgaZcunJmvgB
MUn6gnKhkhBwhVOYe587ayCwGJMU4JoyOo85KOMCgYBWM9jJX7CeRA3vLtahUww+
PfrJz2isEH7dy3MmhF/tOKKpFqQM18f32PJueegUKdNL5wbc9BtLzJSLIzyTNQIU
EHhYNbtvDf0BNacVdI9Ynt/du7TUslUDyrtgdW15YsRw6OAbluYSgoQOCZV06Jk2
I1jVh/ZXGZDgVbq3+zjAgQKBgEAWmD0uxJZX8MN8IAKLAwAlfHHiaY4+8aNszGiQ
K/UojhoO1oOYAJIMOdjxIs7w5drDTHOuJQA19nm9cy0cUsTDAYjPp3FpAUfqiEhv
h8ZIgjiUJq/ZS6k0EXlaPV5cGrZJd51zHyOLWEK2py9/zYHfRm/J0lt34cSxd1x1
23O7AoGBAJVTK+yBAj3Fqr5udeSWSndxGcnvhulx2VRrbkLgSQTyJYJaQStkcK/2
gaVJwMuwAKWYxXV2SIcMXGr7R8RdrFIXPxn2zQSRKxuPNbvRGJ9tzxAX3l3ji1Hk
JLVle16hcB8CZ7kWTQmG3sHxeHqSh7tkmGA4qMIvUjU9Kp8yiapX
-----END RSA PRIVATE KEY-----`
)

// ===== License验证器 =====

// LicenseValidator 用于被授权软件中验证license
type LicenseValidator struct {
	rsaPublicKey  *rsa.PublicKey  // 用于验证数字签名
	rsaPrivateKey *rsa.PrivateKey // 用于解密机器ID（需要嵌入软件）
}

// NewLicenseValidator 创建license验证器
func NewLicenseValidator() (*LicenseValidator, error) {
	// 解析嵌入的公钥
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded public key")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded public key: %v", err)
	}

	// 解析嵌入的私钥
	privateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded private key: %v", err)
	}

	return &LicenseValidator{
		rsaPublicKey:  publicKey,
		rsaPrivateKey: privateKey,
	}, nil
}

// ValidateLicense 验证license文件的完整性和有效性
func (lv *LicenseValidator) ValidateLicense(licenseData *LicenseData) error {
	// 1. 验证license是否过期
	expirationDate, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
	if err != nil {
		return fmt.Errorf("invalid expiration date format: %v", err)
	}

	if time.Now().After(expirationDate) {
		return fmt.Errorf("license has expired on %s", licenseData.ExpirationDate)
	}

	// 2. 验证机器绑定
	err = lv.validateMachineBinding(licenseData.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("machine binding validation failed: %v", err)
	}

	// 3. 验证数字签名
	err = lv.validateSignature(licenseData)
	if err != nil {
		return fmt.Errorf("signature validation failed: %v", err)
	}

	return nil
}

// validateMachineBinding 验证机器绑定
func (lv *LicenseValidator) validateMachineBinding(encryptedMachineID string) error {
	// 获取当前机器的实际机器ID
	currentMachineID, err := lv.getCurrentMachineID()
	if err != nil {
		return fmt.Errorf("failed to get current machine ID: %v", err)
	}

	// 解密license中的机器ID
	licenseMachineID, err := lv.decryptMachineID(encryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt license machine ID: %v", err)
	}

	// 比较机器ID
	if currentMachineID != licenseMachineID {
		return fmt.Errorf("license is not valid for this machine")
	}

	return nil
}

// validateSignature 验证数字签名
func (lv *LicenseValidator) validateSignature(licenseData *LicenseData) error {
	// 重建签名数据
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)

	// 解密机器ID用于签名验证
	decryptedMachineID, err := lv.decryptMachineID(licenseData.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID for signature validation: %v", err)
	}

	sigData := SignatureData{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware, // Software name only
		Version:        licenseData.AuthorizedVersion,  // Software version only
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  lv.hashString(decryptedMachineID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// 创建哈希
	hash := sha256.Sum256(jsonData)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(licenseData.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名
	err = rsa.VerifyPKCS1v15(lv.rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

// decryptMachineID 解密机器ID
func (lv *LicenseValidator) decryptMachineID(encryptedMachineID string) (string, error) {
	// 解码base64
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	// RSA解密
	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, lv.rsaPrivateKey, encryptedData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %v", err)
	}

	return string(decryptedData), nil
}

// getCurrentMachineID 获取当前机器的机器ID
// 这个函数需要根据实际的机器ID获取方式来实现
func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
	// TODO: 实现获取当前机器ID的逻辑
	// 这里应该使用与生成机器信息文件时相同的方法
	// 例如：CPU序列号 + 主板序列号 + 硬盘序列号等

	// 示例实现（需要根据实际情况修改）
	// 这里返回一个示例机器ID，实际使用时需要实现真实的机器ID获取逻辑
	machineID := fmt.Sprintf("%s-%s-%s",
		"711221f2-c02b-4058-b6ac-165578baae25", // CPU ID
		"S9U0BB2481000104",                     // 硬盘序列号
		runtime.GOOS)                           // 操作系统

	return machineID, nil
}

// hashString 创建字符串的SHA256哈希
func (lv *LicenseValidator) hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// LoadLicenseFromFile 从文件加载license
func LoadLicenseFromFile(filePath string) (*LicenseData, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read license file: %v", err)
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license JSON: %v", err)
	}

	return &license, nil
}

// ===== 使用示例 =====

// ValidateLicenseFile 验证license文件（便捷函数）
func ValidateLicenseFile(licenseFilePath string) error {
	// 1. 创建验证器
	validator, err := NewLicenseValidator()
	if err != nil {
		return fmt.Errorf("failed to create validator: %v", err)
	}

	// 2. 加载license文件
	license, err := LoadLicenseFromFile(licenseFilePath)
	if err != nil {
		return fmt.Errorf("failed to load license: %v", err)
	}

	// 3. 验证license
	err = validator.ValidateLicense(license)
	if err != nil {
		return fmt.Errorf("license validation failed: %v", err)
	}

	return nil
}

// ExampleUsageInAuthorizedSoftware 在被授权软件中使用license验证的示例
func ExampleUsageInAuthorizedSoftware() {
	fmt.Println("=== 被授权软件License验证示例 ===")

	// 方法1：使用便捷函数
	err := ValidateLicenseFile("license.json")
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
		fmt.Println("软件将拒绝运行")
		// os.Exit(1) // 实际使用时可以退出程序
		return
	}
	fmt.Println("✅ License验证成功，软件可以正常运行")

	// 方法2：详细验证过程
	validator, err := NewLicenseValidator()
	if err != nil {
		fmt.Printf("创建验证器失败: %v\n", err)
		return
	}

	license, err := LoadLicenseFromFile("license.json")
	if err != nil {
		fmt.Printf("加载license失败: %v\n", err)
		return
	}

	// 显示license信息
	fmt.Printf("License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期日期: %s\n", license.ExpirationDate)

	// 验证license
	err = validator.ValidateLicense(license)
	if err != nil {
		fmt.Printf("License验证失败: %v\n", err)
		return
	}

	fmt.Println("License验证通过，软件功能已解锁")
}

/*
===== 集成说明 =====

1. 复制文件：
   将此文件 (standalone_license_validator.go) 复制到被授权软件项目中

2. 修改package名称：
   将文件开头的 "package main" 改为你的项目包名，如 "package myapp"

3. 在软件启动时验证：
   func main() {
       // 验证license
       err := ValidateLicenseFile("license.json")
       if err != nil {
           log.Fatal("License验证失败:", err)
       }

       // license验证通过，继续运行软件
       fmt.Println("软件已授权，正在启动...")
       // ... 你的软件逻辑
   }

4. 在关键功能点再次验证：
   func criticalFunction() {
       err := ValidateLicenseFile("license.json")
       if err != nil {
           return fmt.Errorf("功能未授权: %v", err)
       }
       // 执行关键功能
   }

5. 安全建议：
   - 使用代码混淆工具保护嵌入的私钥
   - 添加反调试和反逆向工程保护
   - 定期检查license有效性
   - 将license文件路径设为可配置

6. 自定义机器ID获取：
   修改 getCurrentMachineID() 函数，实现与机器信息生成器相同的机器ID获取逻辑

===== 验证流程 =====

1. 过期检查：验证当前日期是否超过 expiration_date
2. 机器绑定：解密 encrypted_machine_id 并与当前机器ID比较
3. 签名验证：使用公钥验证 signature 确保license未被篡改

===== 所需密钥 =====

- RSA公钥：验证license数字签名
- RSA私钥：解密机器ID进行绑定验证

这两个密钥已经嵌入在常量中，无需额外文件。

*/
