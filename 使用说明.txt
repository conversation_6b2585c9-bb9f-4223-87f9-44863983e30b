许可证生成器 - 使用说明
====================

🎉 程序已成功构建并测试完成！

📁 文件说明：
- license-generator-v10-hybrid-solution.exe ← 🌟最新V10版本（混合方案：MachineID一致+签名验证通过，强烈推荐）
- license-generator-v9-gui-fixed.exe ← V9版本（GUI修复版本）
- license-generator-v8-fixed-signature.exe ← V8版本（修复签名验证问题）
- license-generator-v7-enhanced-feedback.exe ← V7版本（增强视觉反馈+进度显示）
- license-generator-v6-improved-ui.exe ← V6版本（改进UI反馈+文件替换修复）
- license-generator-v5-fresh-reload.exe ← V5版本（实时重载机器信息）
- license-generator-v4-english.exe     ← V4版本（英文按钮+所有功能）
- license-generator-v4-final.exe       ← V4版本（factory_license.json默认名）
- license-generator-v4-config.exe      ← V4版本（配置记忆功能）
- license-generator-v3-final.exe       ← V3版本（智能版本提取）
- license-generator-v3-console.exe     ← V3版本（带控制台调试）
- license-generator-updated-v2-gui.exe ← V2版本（纯GUI）
- license-generator-updated-v2.exe     ← V2版本（带控制台）
- license-generator-hybrid-gui.exe     ← 混合策略版（纯GUI）
- license-generator-hybrid.exe         ← 混合策略版（带控制台）
- standalone_license_validator.go      ← 独立验证器（用于被授权软件）
- factory_machine_info.json            ← 示例机器信息文件
- machine_decryption_private_key_to_decryp_factory_machineinfo.pem ← 私钥文件
- config_license_generator_for_factory.json ← 配置文件（自动生成）

🚀 快速开始：
1. 双击 license-generator-v10-hybrid-solution.exe 启动程序
2. 首次使用：点击"Browse"选择机器信息JSON文件（Windows原生对话框，过滤.json）
3. 首次使用：点击"Browse"选择私钥PEM文件（Windows原生对话框，过滤.pem）
4. 再次使用：软件自动记住上次选择的文件路径，无需重复选择
5. 自动显示解密后的MachineID和加密的MachineID
6. 自动填充许可证信息（公司信息、智能提取的软件名称、版本号）
7. 可选择修改软件名称和版本号（以用户输入为准）
8. 选择过期日期（预设选项或自定义）
9. 点击"Generate License"→按钮显示"⏳ Generating..."并禁用
10. 状态栏显示详细进度：📂加载→⚙️处理→🔧初始化→🔐生成→💾保存→✅完成
11. 混合方案：许可证中的MachineID与机器信息文件完全一致（用户可验证）
12. 签名使用原始MachineID创建，确保被授权软件验证通过
13. Windows原生保存对话框→默认文件名"factory_license.json"→支持文件替换
14. 生成成功后显示确认对话框，按钮为英文"OK"

✨ 功能特点：
- 支持RSA2048加密解密
- 自动解密机器ID
- 数字签名验证
- Windows原生文件对话框（自动过滤文件类型）
- 一键生成并保存许可证
- 软件名称和版本号合并显示
- 多种过期日期选项：
  * 1个月+2天
  * 3个月+2天
  * 1年+2天
  * 自定义日期
- 英文界面
- JSON格式导出

🔧 技术规格：
- Go 1.21.1
- Fyne GUI框架
- RSA2048加密
- 紧凑签名设计
- 跨平台支持

✅ 测试状态：
- 文件加载验证: ✓
- RSA私钥加载: ✓  
- 机器ID解密: ✓
- 许可证生成: ✓
- 数字签名创建: ✓
- JSON导出功能: ✓

📞 如有问题，请检查：
1. 确保机器信息文件格式正确
2. 确保私钥文件可访问
3. 确保填写了所有必需字段
4. 确保过期日期在未来

🆕 最新版本更新（V6-Improved-UI）：
- ✅ 按钮反馈：点击"Generate License"时按钮显示"Generating..."并禁用，提供清晰的视觉反馈
- ✅ 文件替换修复：修复Windows保存对话框选择"替换"时文件未实际替换的问题
- ✅ 增强日志：详细记录文件保存过程，便于调试和故障排除
- ✅ 文件验证：保存后验证文件是否成功创建，确保操作完成
- ✅ 实时重载：每次生成license时自动重新读取最新的机器信息文件
- ✅ 数据同步：确保生成的license始终使用最新的机器信息数据
- ✅ 自动更新GUI：重新读取文件后自动更新界面显示的信息
- ✅ 日志记录：详细记录文件重载过程，便于调试
- ✅ 英文按钮：确认对话框按钮改为英文"OK"
- ✅ 默认文件名：保存license时默认文件名为"factory_license.json"
- ✅ 配置记忆功能：自动记住上次选择的文件路径
- ✅ 智能启动：启动时自动加载默认文件并填充信息
- ✅ 用户友好：减少重复选择文件的操作
- ✅ 配置文件：config_license_generator_for_factory.json自动管理
- ✅ 智能版本提取：从GeneratedBy字段自动提取软件名称和版本号
- ✅ 默认值智能填充：GUI自动填充提取的软件名称和版本
- ✅ 用户可自定义：在GUI中可以修改默认值，以用户输入为准
- ✅ 软件名称和版本分离：authorized_software + authorized_version
- ✅ 签发日期重命名：generated_date → issued_date
- ✅ 签名包含分离的软件名称和版本信息
- ✅ 混合安全策略：明文基本信息 + 加密机器绑定
- ✅ 用户友好验证：可直接对比加密MachineID
- ✅ 透明度与安全性平衡：既可读又安全
- ✅ 许可证日期格式改为YYYY-MM-DD（只显示年月日）
- ✅ 界面高度调整为800px（更舒适的布局）
- ✅ Windows原生文件对话框
- ✅ 自动显示解密和加密的MachineID
- ✅ 一键生成并保存许可证

🔐 安全策略优势：
1. 透明性：基本许可证信息明文可读，便于客户确认
2. 安全性：机器绑定信息加密，防止恶意修改
3. 可验证：用户可自行验证许可证是否为其电脑生成
4. 防篡改：数字签名确保许可证完整性
5. 简单性：无需复杂的解密过程

📄 最新许可证格式示例：
{
  "company_name": "gwm",                    // 明文 - 便于识别
  "email": "<EMAIL>",             // 明文 - 便于联系
  "phone": "18191928282",                  // 明文 - 便于联系
  "authorized_software": "LS-DYNA Model License Generate Factory", // 明文 - 软件名称
  "authorized_version": "1.0",             // 明文 - 软件版本
  "expiration_date": "2025-08-10",         // 明文 - 便于查看
  "issued_date": "2025-07-09",             // 明文 - 签发日期（原generated_date）
  "encrypted_machine_id": "GXdvrGcNO4Vj73bZ/3qbxrI8ZQMfDnAwX62P3Um/T/mmsrPOg1...", // 加密 - 机器绑定
  "signature": "KDWe8RX5mDtEbF02gmrkfS0KQVhO/+cOiiH/8lXH+lCi/WzPnh3PaQ5VdSZN..."      // 签名 - 防篡改
}

🔍 用户验证方法：
1. 用户收到许可证文件
2. 对比许可证中的 "encrypted_machine_id"
3. 与自己机器信息文件中的 "MachineID"
4. 如果完全一致 → 许可证是为该电脑生成的 ✅
5. 如果不一致 → 许可证是为其他电脑生成的 ❌

🎯 智能版本提取功能：
📋 功能说明：
- 自动从机器信息文件的GeneratedBy字段提取软件名称和版本号
- 支持 'v' 前缀的版本号格式（如 v2.3.0）
- GUI界面自动填充提取的默认值
- 用户可以在界面中修改，以用户输入为准

📖 提取示例：
输入: "LS-DYNA Model License Generate Factory v2.3.0"
→ 软件名称: "LS-DYNA Model License Generate Factory"
→ 版本号: "2.3.0"

输入: "SomeApp v1.0"
→ 软件名称: "SomeApp"
→ 版本号: "1.0"

输入: "NoVersionSoftware"
→ 软件名称: "NoVersionSoftware"
→ 版本号: ""（空）

🔧 配置记忆功能：
📋 功能说明：
- 自动保存用户选择的文件路径到配置文件
- 下次启动时自动加载并填充默认路径
- 用户可通过Browse按钮重新选择新路径
- 配置文件自动管理，无需手动编辑

📖 配置文件示例：
{
  "machine_info_path": "C:\\path\\to\\factory_machine_info.json",
  "private_key_path": "C:\\path\\to\\machine_decryption_private_key.pem"
}

🎯 使用场景：
- 首次使用：选择文件路径，自动保存到配置
- 日常使用：启动即可，无需重复选择文件
- 更换文件：点击Browse重新选择，自动更新配置
- 多环境：不同目录下可有不同的配置文件

程序已完全可用，可以正常生成许可证！
