# Factory License Generation Complete Guide
# 工厂许可证生成完整指南

## 🎯 Overview / 概述

This guide provides step-by-step instructions for generating factory licenses using the License Generator v15.0. The generated licenses are compatible with authorized software and provide secure machine binding.

本指南提供使用许可证生成器v15.0生成工厂许可证的分步说明。生成的许可证与被授权软件兼容，并提供安全的机器绑定。

## 📋 Prerequisites / 前提条件

### Required Files / 必需文件
1. **License Generator Executable / 许可证生成器可执行文件**
   - `license-generator-v15-final-with-version.exe`
   - Latest version with version display in title bar
   - 最新版本，标题栏显示版本号

2. **Machine Information File / 机器信息文件**
   - `factory_machine_info.json`
   - Contains encrypted machine ID and company information
   - 包含加密机器ID和公司信息

3. **Private Key File / 私钥文件**
   - `machine_decryption_private_key_to_decryp_factory_machineinfo.pem`
   - RSA private key for decryption and signing
   - 用于解密和签名的RSA私钥

4. **Configuration File / 配置文件**
   - `config_license_generator_for_factory.json`
   - Automatically created and managed by the application
   - 由应用程序自动创建和管理

### File Structure Example / 文件结构示例
```
Project_Folder/
├── license-generator-v15-final-with-version.exe
├── factory_machine_info.json
├── machine_decryption_private_key_to_decryp_factory_machineinfo.pem
└── config_license_generator_for_factory.json (auto-generated)
```

## 🚀 Step-by-Step Process / 分步过程

### Step 1: Launch the Application / 第1步：启动应用程序

1. **Double-click** `license-generator-v15-final-with-version.exe`
2. **Verify version** in the window title: "License Generator v15.0"
3. **Wait** for the GUI interface to load

1. **双击** `license-generator-v15-final-with-version.exe`
2. **验证版本** 在窗口标题中："License Generator v15.0"
3. **等待** GUI界面加载

### Step 2: Configure File Paths / 第2步：配置文件路径

#### Machine Info File / 机器信息文件
1. **Click** "Browse" button next to "Machine Info File"
2. **Navigate** to your `factory_machine_info.json` file
3. **Select** the file and click "Open"
4. **Verify** the path appears in the text field

#### Private Key File / 私钥文件
1. **Click** "Browse" button next to "Private Key File"
2. **Navigate** to your `machine_decryption_private_key_to_decryp_factory_machineinfo.pem` file
3. **Select** the file and click "Open"
4. **Verify** the path appears in the text field

**Note**: The application will automatically save these paths for future use.
**注意**: 应用程序会自动保存这些路径供将来使用。

### Step 3: Review Auto-Filled Information / 第3步：检查自动填充信息

The application will automatically extract and display:
应用程序将自动提取并显示：

- **Company Name** / 公司名称
- **Email** / 邮箱
- **Phone** / 电话
- **Software Name** / 软件名称
- **Version** / 版本号
- **Decrypted Machine ID** / 解密的机器ID

**Verify** all information is correct before proceeding.
**验证** 所有信息正确后再继续。

### Step 4: Set Expiration Date / 第4步：设置过期日期

#### Option A: Use Preset (Recommended) / 选项A：使用预设（推荐）
1. **Select** from dropdown: "1 Month + 2 Days", "3 Months + 2 Days", or "1 Year + 2 Days"
2. **Default behavior**: Application uses 6-month expiration for compatibility
3. **Recommended**: Use any preset option for consistent results

#### Option B: Custom Date / 选项B：自定义日期
1. **Select** "Custom Date" from dropdown
2. **Enter** date in format: YYYY-MM-DD (e.g., 2025-12-31)
3. **Ensure** date is in the future

**Important**: For maximum compatibility with authorized software, use preset options.
**重要**: 为了与被授权软件最大兼容，请使用预设选项。

### Step 5: Generate License / 第5步：生成许可证

1. **Click** "Generate License" button
2. **Wait** for processing (progress indicators will show)
3. **Choose** save location when prompted
4. **Save** as `factory_license.json` (recommended filename)

### Step 6: Verify Generated License / 第6步：验证生成的许可证

#### Check File Contents / 检查文件内容
The generated `factory_license.json` should contain:
生成的`factory_license.json`应包含：

```json
{
  "company_name": "Your Company Name",
  "email": "<EMAIL>",
  "phone": "Your Phone Number",
  "authorized_software": "Software Name",
  "authorized_version": "Version Number",
  "expiration_date": "YYYY-MM-DD",
  "issued_date": "YYYY-MM-DD",
  "encrypted_machine_id": "Base64 Encoded Encrypted Machine ID",
  "signature": "Base64 Encoded Digital Signature"
}
```

#### Verify Machine ID Consistency / 验证机器ID一致性
1. **Compare** `encrypted_machine_id` in license file
2. **With** `MachineID` in machine info file
3. **They should be identical** for user verification

## 🔍 Key Technical Points / 关键技术要点

### Hybrid Signature Approach / 混合签名方案
- **Display**: Uses same encrypted MachineID as machine info file
- **Signature**: Uses raw (decrypted) MachineID for correct validation
- **Result**: User can verify license + authorized software can validate

### 混合签名方案
- **显示**: 使用与机器信息文件相同的加密MachineID
- **签名**: 使用原始（解密后）MachineID进行正确验证
- **结果**: 用户可以验证许可证 + 被授权软件可以验证

### Expiration Time Handling / 过期时间处理
- **Compatibility**: Uses 6-month calculation regardless of preset selection
- **Format**: Stores date as YYYY-MM-DD string
- **Signature**: Uses Unix timestamp for signature creation

### 过期时间处理
- **兼容性**: 无论选择哪个预设，都使用6个月计算
- **格式**: 将日期存储为YYYY-MM-DD字符串
- **签名**: 使用Unix时间戳创建签名

## ⚠️ Important Notes / 重要注意事项

### Security Considerations / 安全考虑
1. **Keep private key secure** - Never share the .pem file
2. **Verify file integrity** - Ensure machine info file is not corrupted
3. **Check permissions** - Ensure write access to output directory

### 安全考虑
1. **保护私钥安全** - 永远不要分享.pem文件
2. **验证文件完整性** - 确保机器信息文件未损坏
3. **检查权限** - 确保对输出目录有写入权限

### Compatibility Requirements / 兼容性要求
1. **Windows OS** - Application designed for Windows
2. **File paths** - Use absolute paths for reliability
3. **File encoding** - Ensure JSON files use UTF-8 encoding

### 兼容性要求
1. **Windows操作系统** - 应用程序为Windows设计
2. **文件路径** - 使用绝对路径确保可靠性
3. **文件编码** - 确保JSON文件使用UTF-8编码

## 🛠️ Troubleshooting / 故障排除

### Common Issues / 常见问题

#### Issue: "Failed to load machine info"
**Solution**: 
- Check file path is correct
- Verify JSON syntax is valid
- Ensure file is not corrupted

#### Issue: "Failed to load private key"
**Solution**:
- Verify .pem file format
- Check file permissions
- Ensure key is not password-protected

#### Issue: "Signature verification failed"
**Solution**:
- Use preset expiration options
- Verify machine info file integrity
- Regenerate license with v15.0 application

### 常见问题

#### 问题："加载机器信息失败"
**解决方案**：
- 检查文件路径是否正确
- 验证JSON语法是否有效
- 确保文件未损坏

#### 问题："加载私钥失败"
**解决方案**：
- 验证.pem文件格式
- 检查文件权限
- 确保密钥没有密码保护

#### 问题："签名验证失败"
**解决方案**：
- 使用预设过期选项
- 验证机器信息文件完整性
- 使用v15.0应用程序重新生成许可证

## 📞 Support Information / 支持信息

### Version Information / 版本信息
- **Current Version**: v15.0
- **Release Date**: 2025-07-10
- **Compatibility**: All authorized software versions

### 版本信息
- **当前版本**: v15.0
- **发布日期**: 2025-07-10
- **兼容性**: 所有被授权软件版本

### Success Indicators / 成功指标
✅ **License generated successfully**
✅ **File saved without errors**
✅ **Machine ID consistency verified**
✅ **Compatible with authorized software**

### 成功指标
✅ **许可证生成成功**
✅ **文件保存无错误**
✅ **机器ID一致性验证**
✅ **与被授权软件兼容**

## 📚 Advanced Configuration / 高级配置

### Configuration File Details / 配置文件详情

The `config_license_generator_for_factory.json` file structure:
`config_license_generator_for_factory.json`文件结构：

```json
{
  "version": "v15.0",
  "machine_info_path": "C:\\path\\to\\factory_machine_info.json",
  "private_key_path": "C:\\path\\to\\machine_decryption_private_key.pem"
}
```

### Machine Info File Format / 机器信息文件格式

Expected `factory_machine_info.json` structure:
预期的`factory_machine_info.json`结构：

```json
{
  "CompanyName": "Company Name",
  "Email": "<EMAIL>",
  "Phone": "Phone Number",
  "GeneratedBy": "Software Name v1.0.0",
  "MachineID": "Base64EncodedEncryptedMachineID...",
  "GeneratedDate": "2025-07-10",
  "Notes": "Additional notes",
  "Version": "Optional version field"
}
```

### Signature Data Structure / 签名数据结构

Internal signature JSON format (for reference):
内部签名JSON格式（供参考）：

```json
{
  "c": "Company Name",
  "e": "<EMAIL>",
  "s": "Software Name",
  "v": "Version",
  "x": **********,
  "m": "HashedMachineID"
}
```

## 🔐 Security Implementation / 安全实现

### Encryption Details / 加密详情
- **Algorithm**: RSA-2048 with OAEP padding
- **Hash Function**: SHA-256
- **Signature**: PKCS1v15 with SHA-256
- **Encoding**: Base64 for all encrypted data

### 加密详情
- **算法**: RSA-2048 with OAEP填充
- **哈希函数**: SHA-256
- **签名**: PKCS1v15 with SHA-256
- **编码**: 所有加密数据使用Base64

### Validation Process / 验证过程
1. **Decrypt** machine ID from license
2. **Reconstruct** signature data JSON
3. **Hash** the JSON string with SHA-256
4. **Verify** signature using public key
5. **Check** expiration date

### 验证过程
1. **解密** 许可证中的机器ID
2. **重构** 签名数据JSON
3. **哈希** JSON字符串使用SHA-256
4. **验证** 使用公钥验证签名
5. **检查** 过期日期

## 📋 Quality Assurance Checklist / 质量保证检查清单

### Before Generation / 生成前检查
- [ ] Machine info file is valid JSON
- [ ] Private key file is accessible
- [ ] Output directory has write permissions
- [ ] Application version is v15.0

### After Generation / 生成后检查
- [ ] License file created successfully
- [ ] File size is reasonable (typically 1-2KB)
- [ ] JSON structure is valid
- [ ] Machine ID matches source file
- [ ] Expiration date is in future

### 生成前检查
- [ ] 机器信息文件是有效的JSON
- [ ] 私钥文件可访问
- [ ] 输出目录有写入权限
- [ ] 应用程序版本是v15.0

### 生成后检查
- [ ] 许可证文件创建成功
- [ ] 文件大小合理（通常1-2KB）
- [ ] JSON结构有效
- [ ] 机器ID与源文件匹配
- [ ] 过期日期在未来

---

**Note**: This guide is for License Generator v15.0. For other versions, please refer to the appropriate documentation.

**注意**: 本指南适用于许可证生成器v15.0。对于其他版本，请参考相应的文档。
