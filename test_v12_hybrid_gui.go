// 测试V12混合GUI版本
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type MachineInfo struct {
	CompanyName string `json:"CompanyName"`
	Email       string `json:"Email"`
	Phone       string `json:"Phone"`
	GeneratedBy string `json:"GeneratedBy"`
	MachineID   string `json:"MachineID"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}
	
	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}
	
	return privateKey, nil
}

func validateLicense(filename string) error {
	fmt.Printf("🔍 Validating %s\n", filename)
	fmt.Println("=" + fmt.Sprintf("%*s", len(filename)+15, "="))
	
	// 1. 加载许可证
	licenseData, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read license: %v", err)
	}
	
	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		return fmt.Errorf("failed to parse license: %v", err)
	}
	
	fmt.Printf("📋 License Info:\n")
	fmt.Printf("  Company: %s\n", license.CompanyName)
	fmt.Printf("  Email: %s\n", license.Email)
	fmt.Printf("  Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  Expiration: %s\n", license.ExpirationDate)
	fmt.Printf("  Issued: %s\n", license.IssuedDate)
	
	// 2. 加载私钥
	privateKey, err := loadPrivateKey()
	if err != nil {
		return fmt.Errorf("failed to load private key: %v", err)
	}
	
	// 3. 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}
	
	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}
	
	fmt.Printf("🔓 Decrypted Machine ID: %s\n", string(decryptedMachineID))
	
	// 4. 重构签名数据
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}
	
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}
	
	fmt.Printf("📋 Signature JSON: %s\n", string(jsonData))
	
	// 5. 验证签名
	hash := sha256.Sum256(jsonData)
	
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}
	
	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	
	fmt.Println("✅ Signature verification successful!")
	return nil
}

func compareWithHybridVersion() {
	fmt.Println("\n🔍 Comparing with Hybrid Version")
	fmt.Println("================================")
	
	// 加载机器信息文件
	machineData, err := os.ReadFile("factory_machine_info.json")
	if err != nil {
		fmt.Printf("❌ Cannot load machine info: %v\n", err)
		return
	}
	
	var machineInfo MachineInfo
	err = json.Unmarshal(machineData, &machineInfo)
	if err != nil {
		fmt.Printf("❌ Cannot parse machine info: %v\n", err)
		return
	}
	
	fmt.Printf("📂 Machine Info MachineID: %s...\n", machineInfo.MachineID[:50])
	
	// 加载混合版本
	hybridData, err := os.ReadFile("hybrid_factory_license.json")
	if err != nil {
		fmt.Printf("❌ Cannot load hybrid version: %v\n", err)
		return
	}
	
	var hybridLicense LicenseData
	err = json.Unmarshal(hybridData, &hybridLicense)
	if err != nil {
		fmt.Printf("❌ Cannot parse hybrid version: %v\n", err)
		return
	}
	
	// 加载GUI生成的版本
	guiData, err := os.ReadFile("factory_license.json")
	if err != nil {
		fmt.Printf("❌ Cannot load GUI version: %v\n", err)
		return
	}
	
	var guiLicense LicenseData
	err = json.Unmarshal(guiData, &guiLicense)
	if err != nil {
		fmt.Printf("❌ Cannot parse GUI version: %v\n", err)
		return
	}
	
	fmt.Printf("📋 Comparison:\n")
	fmt.Printf("  Machine Info MachineID:    %s...\n", machineInfo.MachineID[:50])
	fmt.Printf("  Hybrid Version MachineID:  %s...\n", hybridLicense.EncryptedMachineID[:50])
	fmt.Printf("  GUI Version MachineID:     %s...\n", guiLicense.EncryptedMachineID[:50])
	
	// 检查MachineID一致性
	machineIDMatch := (guiLicense.EncryptedMachineID == machineInfo.MachineID)
	hybridMatch := (hybridLicense.EncryptedMachineID == machineInfo.MachineID)
	guiHybridMatch := (guiLicense.EncryptedMachineID == hybridLicense.EncryptedMachineID)
	
	fmt.Printf("\n🔍 MachineID Consistency Check:\n")
	if machineIDMatch {
		fmt.Println("✅ GUI MachineID matches machine info file")
	} else {
		fmt.Println("❌ GUI MachineID does not match machine info file")
	}
	
	if hybridMatch {
		fmt.Println("✅ Hybrid MachineID matches machine info file")
	} else {
		fmt.Println("❌ Hybrid MachineID does not match machine info file")
	}
	
	if guiHybridMatch {
		fmt.Println("✅ GUI and Hybrid MachineIDs match!")
		fmt.Println("   This means V12 is using the correct hybrid approach.")
	} else {
		fmt.Println("❌ GUI and Hybrid MachineIDs differ")
		fmt.Println("   V12 may need further adjustment.")
	}
}

func main() {
	fmt.Println("🚀 Testing V12 Hybrid GUI Version")
	fmt.Println("=================================")
	
	fmt.Println("Please use license-generator-v12-hybrid-gui.exe to generate a new factory_license.json")
	fmt.Println("This version should use the same logic as the working hybrid_factory_license.json")
	fmt.Println("")
	
	// 测试已知工作的混合版本
	if _, err := os.Stat("hybrid_factory_license.json"); err == nil {
		fmt.Println("🧪 Testing Known Working Hybrid Version...")
		err := validateLicense("hybrid_factory_license.json")
		if err != nil {
			fmt.Printf("❌ Hybrid version failed: %v\n", err)
		} else {
			fmt.Println("✅ Hybrid version validation successful!")
		}
	}
	
	// 测试GUI生成的版本
	if _, err := os.Stat("factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing GUI Generated Version...")
		err := validateLicense("factory_license.json")
		if err != nil {
			fmt.Printf("❌ GUI version failed: %v\n", err)
		} else {
			fmt.Println("✅ GUI version validation successful!")
		}
	}
	
	// 比较版本
	compareWithHybridVersion()
	
	fmt.Println("\n📋 Test Summary:")
	fmt.Println("- If GUI version passes validation, V12 fix is successful")
	fmt.Println("- If GUI and Hybrid MachineIDs match, the hybrid approach is working")
	fmt.Println("- If MachineID matches machine info file, user verification is preserved")
}
