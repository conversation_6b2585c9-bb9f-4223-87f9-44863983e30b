# License Signature Keys Information
# 许可证签名密钥信息

## 📋 Overview / 概述

This document describes the RSA key pair used for license signature verification in the license validation system.

本文档描述了许可证验证系统中用于许可证签名验证的RSA密钥对。

## 🔑 Key Files / 密钥文件

### 1. Public Key / 公钥
**File**: `license_signature_public_key.pem`
**Purpose**: Used for verifying digital signatures of license files
**用途**: 用于验证许可证文件的数字签名

```
Format: RSA Public Key (PKCS#1)
Key Size: 2048 bits
Usage: Signature Verification
```

### 2. Private Key / 私钥
**File**: `license_signature_private_key.pem`
**Purpose**: Used for decrypting machine ID binding verification
**用途**: 用于解密机器ID绑定验证

```
Format: RSA Private Key (PKCS#1)
Key Size: 2048 bits
Usage: Machine ID Decryption
```

## 🔒 Security Model / 安全模型

### Dual-Purpose Key Pair / 双用途密钥对

This RSA key pair serves two critical security functions:
此RSA密钥对具有两个关键的安全功能：

#### 1. Digital Signature Verification / 数字签名验证
- **Public Key Usage**: Verifies the authenticity and integrity of license files
- **Process**: License generator signs license data → Validator verifies signature
- **Security**: Prevents license tampering and ensures authenticity

- **公钥用途**: 验证许可证文件的真实性和完整性
- **流程**: 许可证生成器签名许可证数据 → 验证器验证签名
- **安全性**: 防止许可证篡改并确保真实性

#### 2. Machine ID Decryption / 机器ID解密
- **Private Key Usage**: Decrypts machine ID for binding verification
- **Process**: Encrypted machine ID in license → Decrypted for comparison
- **Security**: Ensures license is bound to specific hardware

- **私钥用途**: 解密机器ID进行绑定验证
- **流程**: 许可证中的加密机器ID → 解密后进行比较
- **安全性**: 确保许可证绑定到特定硬件

## 📁 Key Usage in System / 系统中的密钥使用

### In License Generator / 在许可证生成器中
```go
// Uses the SAME private key for:
// 使用相同的私钥用于：
1. Signing license data (digital signature)
   签名许可证数据（数字签名）
2. Encrypting machine ID (machine binding)
   加密机器ID（机器绑定）
```

### In License Validator / 在许可证验证器中
```go
// Uses the SAME key pair for:
// 使用相同的密钥对用于：
1. Public key: Verify digital signature
   公钥：验证数字签名
2. Private key: Decrypt machine ID
   私钥：解密机器ID
```

## ⚠️ Security Considerations / 安全考虑

### 1. Key Protection / 密钥保护

#### Private Key Security / 私钥安全
- **Critical**: The private key must be embedded in authorized software
- **Risk**: If compromised, attackers could decrypt machine IDs
- **Mitigation**: Use code obfuscation and anti-reverse engineering measures

- **关键**: 私钥必须嵌入到被授权软件中
- **风险**: 如果泄露，攻击者可以解密机器ID
- **缓解**: 使用代码混淆和反逆向工程措施

#### Public Key Security / 公钥安全
- **Usage**: Safe to embed in validation software
- **Purpose**: Only used for signature verification
- **Risk**: Low - cannot be used to forge signatures

- **使用**: 可以安全地嵌入到验证软件中
- **目的**: 仅用于签名验证
- **风险**: 低 - 无法用于伪造签名

### 2. Key Rotation / 密钥轮换

If keys need to be rotated:
如果需要轮换密钥：

1. Generate new RSA key pair
2. Update license generator with new keys
3. Update all authorized software with new embedded keys
4. Regenerate all active licenses

1. 生成新的RSA密钥对
2. 使用新密钥更新许可证生成器
3. 使用新的嵌入密钥更新所有被授权软件
4. 重新生成所有活跃的许可证

## 🔧 Technical Details / 技术详情

### Key Specifications / 密钥规格
```
Algorithm: RSA
Key Size: 2048 bits
Format: PKCS#1 PEM
Padding: OAEP (for encryption), PKCS1v15 (for signature)
Hash: SHA-256
```

### Key Fingerprints / 密钥指纹
```bash
# To verify key integrity, you can check fingerprints:
# 要验证密钥完整性，可以检查指纹：

# Public key fingerprint
openssl rsa -pubin -in license_signature_public_key.pem -noout -modulus | openssl md5

# Private key fingerprint  
openssl rsa -in license_signature_private_key.pem -noout -modulus | openssl md5
```

## 📋 Integration Checklist / 集成检查清单

For authorized software developers:
对于被授权软件开发者：

- [ ] Embed both public and private keys in `standalone_license_validator.go`
- [ ] Ensure keys are protected with code obfuscation
- [ ] Test signature verification with actual license files
- [ ] Test machine ID decryption functionality
- [ ] Implement proper error handling for key operations

- [ ] 在 `standalone_license_validator.go` 中嵌入公钥和私钥
- [ ] 确保密钥通过代码混淆得到保护
- [ ] 使用实际许可证文件测试签名验证
- [ ] 测试机器ID解密功能
- [ ] 为密钥操作实现适当的错误处理

## 🚨 Important Notes / 重要说明

### 1. Key Consistency / 密钥一致性
**CRITICAL**: The keys in `standalone_license_validator.go` MUST match these exact keys.
**关键**: `standalone_license_validator.go` 中的密钥必须与这些确切的密钥匹配。

### 2. Backup and Recovery / 备份和恢复
- Keep secure backups of these keys
- Store in encrypted form when not in use
- Document key recovery procedures

- 保留这些密钥的安全备份
- 不使用时以加密形式存储
- 记录密钥恢复程序

### 3. Compliance / 合规性
- Ensure key usage complies with local regulations
- Consider export control restrictions for cryptographic software
- Document key management procedures for audits

- 确保密钥使用符合当地法规
- 考虑加密软件的出口管制限制
- 为审计记录密钥管理程序

## 📞 Support / 支持

For questions about key usage or security:
有关密钥使用或安全的问题：

1. Verify key integrity using fingerprints
2. Test with known good license files
3. Check error messages for specific key-related issues
4. Contact system administrator for key rotation procedures

1. 使用指纹验证密钥完整性
2. 使用已知良好的许可证文件进行测试
3. 检查特定密钥相关问题的错误消息
4. 联系系统管理员了解密钥轮换程序

---

**Generated**: 2025-07-10
**Key Pair**: RSA-2048 for License Signature System
**Status**: Active
