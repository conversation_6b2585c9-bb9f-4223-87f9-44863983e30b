// check_correct_keys.go
// 检查正确生成的密钥文件

package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"math/big"
	"os"
)

func main() {
	fmt.Println("🔍 Checking CORRECT Key Files")
	fmt.Println("=============================")

	// 加载四个正确的密钥文件
	machinePriv, err := loadPrivKey("machine_id_decryption_private_key_CORRECT.pem")
	if err != nil {
		fmt.Printf("❌ Machine private key error: %v\n", err)
		return
	}

	machinePub, err := loadPubKey("machine_id_decryption_public_key_CORRECT.pem")
	if err != nil {
		fmt.Printf("❌ Machine public key error: %v\n", err)
		return
	}

	sigPriv, err := loadPrivKey("signature_generation_private_key_CORRECT.pem")
	if err != nil {
		fmt.Printf("❌ Signature private key error: %v\n", err)
		return
	}

	sigPub, err := loadPubKey("signature_verification_public_key_CORRECT.pem")
	if err != nil {
		fmt.Printf("❌ Signature public key error: %v\n", err)
		return
	}

	fmt.Println("✅ All four CORRECT keys loaded")

	// 检查关键问题：机器私钥是否与签名公钥配对
	machinePrivPub := &machinePriv.PublicKey
	
	fmt.Println("\n🔍 Critical Check (What Authorized Software Checks):")
	fmt.Printf("Machine Private Key N: %s\n", getFingerprint(machinePriv.N))
	fmt.Printf("Signature Public Key N: %s\n", getFingerprint(sigPub.N))
	
	if machinePrivPub.N.Cmp(sigPub.N) == 0 {
		fmt.Println("❌ PROBLEM: Machine private key ↔ Signature public key are SAME pair!")
		fmt.Println("   被授权软件反馈正确: 不是两对不同的密钥")
	} else {
		fmt.Println("✅ EXCELLENT: Machine private key ↔ Signature public key are DIFFERENT pairs")
		fmt.Println("   密钥分离成功! 被授权软件应该满意了!")
	}

	// 检查所有组合
	fmt.Println("\n📋 All Key Combinations:")
	fmt.Printf("Machine Priv ↔ Machine Pub: %s\n", checkMatch(machinePriv.N, machinePub.N))
	fmt.Printf("Signature Priv ↔ Signature Pub: %s\n", checkMatch(sigPriv.N, sigPub.N))
	fmt.Printf("Machine Priv ↔ Signature Pub: %s\n", checkMatch(machinePriv.N, sigPub.N))
	fmt.Printf("Signature Priv ↔ Machine Pub: %s\n", checkMatch(sigPriv.N, machinePub.N))

	// 显示密钥指纹
	fmt.Println("\n📋 Key Fingerprints:")
	fmt.Printf("🔑 Machine Private:    %s\n", getFingerprint(machinePriv.N))
	fmt.Printf("🔓 Machine Public:     %s\n", getFingerprint(machinePub.N))
	fmt.Printf("✍️  Signature Private:  %s\n", getFingerprint(sigPriv.N))
	fmt.Printf("🔍 Signature Public:   %s\n", getFingerprint(sigPub.N))

	// 最终结论
	fmt.Println("\n🎯 Final Analysis:")
	fmt.Println("==================")
	
	machineKeyPairValid := machinePriv.N.Cmp(machinePub.N) == 0
	signatureKeyPairValid := sigPriv.N.Cmp(sigPub.N) == 0
	keyPairsDifferent := machinePriv.N.Cmp(sigPriv.N) != 0
	noCrossMatch := machinePrivPub.N.Cmp(sigPub.N) != 0

	if machineKeyPairValid && signatureKeyPairValid && keyPairsDifferent && noCrossMatch {
		fmt.Println("🎉 SUCCESS: Perfect key separation achieved!")
		fmt.Println("   ✅ Machine key pair is valid")
		fmt.Println("   ✅ Signature key pair is valid")
		fmt.Println("   ✅ Two key pairs are completely different")
		fmt.Println("   ✅ No cross-matching (the critical issue is solved)")
		fmt.Println()
		fmt.Println("   被授权软件现在应该满意了!")
		fmt.Println("   这四个密钥文件是真正分离的两对密钥!")
	} else {
		fmt.Println("❌ Still have problems:")
		if !machineKeyPairValid {
			fmt.Println("   - Machine key pair invalid")
		}
		if !signatureKeyPairValid {
			fmt.Println("   - Signature key pair invalid")
		}
		if !keyPairsDifferent {
			fmt.Println("   - Key pairs are not different")
		}
		if !noCrossMatch {
			fmt.Println("   - Cross-matching still exists")
		}
	}
}

func loadPrivKey(filename string) (*rsa.PrivateKey, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, fmt.Errorf("no PEM block")
	}
	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

func loadPubKey(filename string) (*rsa.PublicKey, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, fmt.Errorf("no PEM block")
	}
	return x509.ParsePKCS1PublicKey(block.Bytes)
}

func getFingerprint(n *big.Int) string {
	bytes := n.Bytes()
	if len(bytes) >= 4 {
		return fmt.Sprintf("%x", bytes[:4])
	}
	return fmt.Sprintf("%x", bytes)
}

func checkMatch(n1, n2 *big.Int) string {
	if n1.Cmp(n2) == 0 {
		return "SAME ✅ (should be same for key pairs)"
	}
	return "DIFFERENT ✅ (should be different between pairs)"
}
