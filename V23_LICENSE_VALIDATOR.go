// v23_license_validator.go
// V23专用License验证器 - 专门验证包含License Type和Start Date的新格式license
// 简化设计，无需向后兼容，专注于V23功能

package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"runtime"
	"time"
)

// ===== V23 License数据结构 =====

// V23LicenseData represents the V23 license format with all new fields
// V23许可证数据结构，包含所有新字段
type V23LicenseData struct {
	// 基础信息
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`

	// V22+ 新增字段
	LicenseType string `json:"license_type"` // 必需：lease, demo, perpetual

	// V23+ 新增字段
	StartDate      string `json:"start_date"`      // 必需：YYYY-MM-DD格式
	ExpirationDate string `json:"expiration_date"` // 必需：YYYY-MM-DD格式
	IssuedDate     string `json:"issued_date"`     // 必需：YYYY-MM-DD格式

	// 安全字段
	EncryptedMachineID string `json:"encrypted_machine_id"` // 必需：机器绑定
	Signature          string `json:"signature"`            // 必需：数字签名
}

// V23SignatureData represents the signature data for V23 licenses
// V23签名数据结构，不包含公司名称、邮箱、电话字段
// Note: Company name, email, and phone are NOT included in signature verification
type V23SignatureData struct {
	Software       string `json:"s"` // 软件名称
	Version        string `json:"v"` // 软件版本
	LicenseType    string `json:"t"` // 许可证类型
	StartUnix      int64  `json:"b"` // 开始日期Unix时间戳
	ExpirationUnix int64  `json:"x"` // 过期日期Unix时间戳
	MachineIDHash  string `json:"m"` // 机器ID哈希
}

// ===== 嵌入的密钥常量 =====

const (
	// RSA公钥 - 用于验证license签名 (新的独立签名密钥)
	V23_SIGNING_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

	// RSA私钥 - 用于解密机器ID绑定验证
	V23_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

// ===== V23 License验证器 =====

// V23LicenseValidator V23专用license验证器
type V23LicenseValidator struct {
	rsaPublicKey  *rsa.PublicKey
	rsaPrivateKey *rsa.PrivateKey
}

// NewV23LicenseValidator 创建V23专用license验证器
func NewV23LicenseValidator() (*V23LicenseValidator, error) {
	// 解析嵌入的签名公钥 (新的独立签名密钥)
	publicKeyBlock, _ := pem.Decode([]byte(V23_SIGNING_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded signing public key")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded public key: %v", err)
	}

	// 解析嵌入的私钥
	privateKeyBlock, _ := pem.Decode([]byte(V23_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded private key: %v", err)
	}

	return &V23LicenseValidator{
		rsaPublicKey:  publicKey,
		rsaPrivateKey: privateKey,
	}, nil
}

// ValidateV23License 验证V23格式的license文件
func (v23 *V23LicenseValidator) ValidateV23License(license *V23LicenseData) error {
	fmt.Println("🔍 Validating V23 License...")

	// 1. 验证必需字段
	err := v23.validateRequiredFields(license)
	if err != nil {
		return fmt.Errorf("required fields validation failed: %v", err)
	}

	// 2. 验证License Type
	err = v23.validateLicenseType(license.LicenseType)
	if err != nil {
		return fmt.Errorf("license type validation failed: %v", err)
	}

	// 3. 验证日期逻辑
	err = v23.validateDateLogic(license.StartDate, license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("date validation failed: %v", err)
	}

	// 4. 验证机器绑定
	err = v23.validateMachineBinding(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("machine binding validation failed: %v", err)
	}

	// 5. 验证数字签名
	err = v23.validateSignature(license)
	if err != nil {
		return fmt.Errorf("signature validation failed: %v", err)
	}

	fmt.Println("✅ V23 License validation successful!")
	return nil
}

// validateRequiredFields 验证必需字段
func (v23 *V23LicenseValidator) validateRequiredFields(license *V23LicenseData) error {
	if license.CompanyName == "" {
		return fmt.Errorf("company_name is required")
	}
	if license.Email == "" {
		return fmt.Errorf("email is required")
	}
	if license.AuthorizedSoftware == "" {
		return fmt.Errorf("authorized_software is required")
	}
	if license.AuthorizedVersion == "" {
		return fmt.Errorf("authorized_version is required")
	}
	if license.LicenseType == "" {
		return fmt.Errorf("license_type is required")
	}
	if license.StartDate == "" {
		return fmt.Errorf("start_date is required")
	}
	if license.ExpirationDate == "" {
		return fmt.Errorf("expiration_date is required")
	}
	if license.EncryptedMachineID == "" {
		return fmt.Errorf("encrypted_machine_id is required")
	}
	if license.Signature == "" {
		return fmt.Errorf("signature is required")
	}

	return nil
}

// validateLicenseType 验证许可证类型
func (v23 *V23LicenseValidator) validateLicenseType(licenseType string) error {
	validTypes := []string{"lease", "demo", "perpetual"}

	for _, validType := range validTypes {
		if licenseType == validType {
			fmt.Printf("✅ Valid license type: %s\n", licenseType)
			return nil
		}
	}

	return fmt.Errorf("invalid license type: %s (must be: lease, demo, or perpetual)", licenseType)
}

// validateDateLogic 验证日期逻辑
func (v23 *V23LicenseValidator) validateDateLogic(startDateStr, expirationDateStr string) error {
	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return fmt.Errorf("invalid start_date format: %v (expected: YYYY-MM-DD)", err)
	}

	// 解析过期日期
	expirationDate, err := time.Parse("2006-01-02", expirationDateStr)
	if err != nil {
		return fmt.Errorf("invalid expiration_date format: %v (expected: YYYY-MM-DD)", err)
	}

	// 验证过期日期必须在未来
	now := time.Now()
	if expirationDate.Before(now) {
		return fmt.Errorf("license has expired on %s", expirationDateStr)
	}

	// 验证过期日期必须晚于开始日期
	if expirationDate.Before(startDate) || expirationDate.Equal(startDate) {
		return fmt.Errorf("expiration_date (%s) must be after start_date (%s)", expirationDateStr, startDateStr)
	}

	// 验证许可证是否已经生效
	if now.Before(startDate) {
		return fmt.Errorf("license is not yet active (starts on %s)", startDateStr)
	}

	// 计算许可证有效期
	duration := expirationDate.Sub(startDate)
	activeDays := int(duration.Hours() / 24)
	remainingDays := int(expirationDate.Sub(now).Hours() / 24)

	fmt.Printf("✅ License active period: %s to %s (%d days total, %d days remaining)\n",
		startDateStr, expirationDateStr, activeDays, remainingDays)

	return nil
}

// validateMachineBinding 验证机器绑定
func (v23 *V23LicenseValidator) validateMachineBinding(encryptedMachineID string) error {
	// 获取当前机器ID
	currentMachineID, err := v23.getCurrentMachineID()
	if err != nil {
		return fmt.Errorf("failed to get current machine ID: %v", err)
	}

	// 解密license中的机器ID
	licenseMachineID, err := v23.decryptMachineID(encryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt license machine ID: %v", err)
	}

	// 比较机器ID
	if currentMachineID != licenseMachineID {
		return fmt.Errorf("license is not valid for this machine (machine ID mismatch)")
	}

	fmt.Println("✅ Machine binding validation successful")
	return nil
}

// validateSignature 验证数字签名
func (v23 *V23LicenseValidator) validateSignature(license *V23LicenseData) error {
	// 解密机器ID用于签名验证
	decryptedMachineID, err := v23.decryptMachineID(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID for signature validation: %v", err)
	}

	// 解析日期
	startTime, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		return fmt.Errorf("failed to parse start date for signature: %v", err)
	}

	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date for signature: %v", err)
	}

	// 构建签名数据 (不包含公司名称、邮箱、电话)
	// Note: Company name, email, and phone are NOT included in signature verification
	sigData := V23SignatureData{
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  v23.hashString(decryptedMachineID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// 计算哈希
	hash := sha256.Sum256(jsonData)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名
	err = rsa.VerifyPKCS1v15(v23.rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	fmt.Println("✅ Digital signature validation successful")
	return nil
}

// decryptMachineID 解密机器ID
func (v23 *V23LicenseValidator) decryptMachineID(encryptedMachineID string) (string, error) {
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, v23.rsaPrivateKey, encryptedData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %v", err)
	}

	return string(decryptedData), nil
}

// getCurrentMachineID 获取当前机器ID
func (v23 *V23LicenseValidator) getCurrentMachineID() (string, error) {
	// TODO: 实现与生成器相同的机器ID获取逻辑
	// 这里使用示例值，实际使用时需要实现真实的机器ID获取
	machineID := fmt.Sprintf("%s-%s-%s",
		"711221f2-c02b-4058-b6ac-165578baae25", // CPU ID
		"S9U0BB2481000104",                     // 硬盘序列号
		runtime.GOOS)                           // 操作系统
	return machineID, nil
}

// hashString 创建字符串的SHA256哈希
func (v23 *V23LicenseValidator) hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// LoadV23LicenseFromFile 从文件加载V23格式的license
func LoadV23LicenseFromFile(filePath string) (*V23LicenseData, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read license file: %v", err)
	}

	var license V23LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license JSON: %v", err)
	}

	return &license, nil
}

// ===== 便捷函数 =====

// ValidateV23LicenseFile 验证V23格式的license文件（便捷函数）
func ValidateV23LicenseFile(licenseFilePath string) error {
	validator, err := NewV23LicenseValidator()
	if err != nil {
		return fmt.Errorf("failed to create V23 validator: %v", err)
	}

	license, err := LoadV23LicenseFromFile(licenseFilePath)
	if err != nil {
		return fmt.Errorf("failed to load V23 license: %v", err)
	}

	err = validator.ValidateV23License(license)
	if err != nil {
		return fmt.Errorf("V23 license validation failed: %v", err)
	}

	return nil
}

// ===== 使用示例 =====

// ExampleV23Usage V23 license验证使用示例
func ExampleV23Usage() {
	fmt.Println("=== V23 License验证器示例 ===")

	// 方法1：使用便捷函数（推荐）
	err := ValidateV23LicenseFile("license.json")
	if err != nil {
		fmt.Printf("❌ V23 License验证失败: %v\n", err)
		return
	}
	fmt.Println("✅ V23 License验证成功")

	// 方法2：详细验证过程
	validator, err := NewV23LicenseValidator()
	if err != nil {
		fmt.Printf("创建V23验证器失败: %v\n", err)
		return
	}

	license, err := LoadV23LicenseFromFile("license.json")
	if err != nil {
		fmt.Printf("加载V23 license失败: %v\n", err)
		return
	}

	// 显示license信息
	fmt.Printf("\n📋 V23 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", license.LicenseType)
	fmt.Printf("  开始: %s\n", license.StartDate)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  签发: %s\n", license.IssuedDate)

	// 验证license
	err = validator.ValidateV23License(license)
	if err != nil {
		fmt.Printf("❌ V23 License验证失败: %v\n", err)
		return
	}

	fmt.Println("✅ V23 License验证通过，软件功能已解锁")
}

// ===== 集成示例 =====

// ExampleIntegration 示例：在软件启动时验证license
func ExampleIntegration() {
	fmt.Println("🚀 启动软件...")

	// 验证V23 license
	err := ValidateV23LicenseFile("license.json")
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
		fmt.Println("软件无法启动，请检查license文件")
		os.Exit(1)
	}

	fmt.Println("✅ License验证成功")
	fmt.Println("🎉 软件已授权，正在启动...")

	// 这里继续你的软件逻辑
	// startYourApplication()
}

/*
===== V23 License验证器使用指南 =====

## 快速集成

### 1. 复制验证器文件
将 V23_LICENSE_VALIDATOR.go 复制到你的项目中

### 2. 更新包名
```go
// 将第一行改为你的包名
package yourpackage
```

### 3. 在软件中集成
```go
func main() {
    // 验证V23 license
    err := ValidateV23LicenseFile("license.json")
    if err != nil {
        log.Fatal("License验证失败:", err)
    }

    fmt.Println("软件已授权，正在启动...")
    // 继续你的软件逻辑
}
```

## V23 License格式要求

V23格式的license文件必须包含以下字段：

### 必需字段
- company_name: 公司名称
- email: 邮箱地址
- phone: 电话号码
- authorized_software: 授权软件名称
- authorized_version: 授权软件版本
- license_type: 许可证类型 (lease/demo/perpetual)
- start_date: 开始日期 (YYYY-MM-DD)
- expiration_date: 过期日期 (YYYY-MM-DD)
- issued_date: 签发日期 (YYYY-MM-DD)
- encrypted_machine_id: 加密的机器ID
- signature: 数字签名

### 验证逻辑
1. ✅ 必需字段完整性检查
2. ✅ License Type有效性验证 (lease/demo/perpetual)
3. ✅ 日期格式和逻辑验证
4. ✅ 机器绑定验证
5. ✅ 数字签名验证

## 错误处理

验证器会返回详细的错误信息：
- 字段缺失错误
- 日期格式错误
- 许可证过期错误
- 机器绑定失败错误
- 签名验证失败错误

## 性能特性

- 🚀 专门优化的V23验证逻辑
- 🔒 完整的安全验证
- 📝 详细的验证日志
- ⚡ 快速验证响应

*/
