# Company ID Feature Demo
# 公司ID功能演示

## Demo Scenario / 演示场景

This demo shows how to use the new Company ID feature in the License Generator V27.

本演示展示如何在许可证生成器V27中使用新的公司ID功能。

## Step-by-Step Demo / 分步演示

### Step 1: Launch the Application / 步骤1：启动应用程序

```bash
.\license-generator-v27-with-company-id.exe
```

### Step 2: Load Files / 步骤2：加载文件

1. **Machine Info File**: Select your `factory_machine_info.json`
2. **Private Key File**: Select your machine decryption key
3. **Verify**: Check that Machine ID is decrypted and displayed

1. **机器信息文件**：选择您的`factory_machine_info.json`
2. **私钥文件**：选择您的机器解密密钥
3. **验证**：检查机器ID是否已解密并显示

### Step 3: Company ID Input Options / 步骤3：公司ID输入选项

#### Option A: Automatic Assignment / 选项A：自动分配

1. **Leave Company ID field empty**
2. The system will automatically:
   - Check if company exists in registry
   - Assign existing ID or create new one
   - Start from `100-0000` for new companies

1. **保持公司ID字段为空**
2. 系统将自动：
   - 检查公司是否存在于注册表中
   - 分配现有ID或创建新ID
   - 新公司从`100-0000`开始

#### Option B: Manual Assignment / 选项B：手动分配

1. **Enter 7-digit ID**: Type `123-4567` (or any valid format)
2. **Auto-formatting**: System formats as you type
3. **Validation**: Real-time validation of format and uniqueness

1. **输入7位数字ID**：输入`123-4567`（或任何有效格式）
2. **自动格式化**：系统在您输入时格式化
3. **验证**：实时验证格式和唯一性

### Step 4: Complete License Details / 步骤4：完成许可证详情

Fill in the remaining fields:
- **Company Name**: (auto-filled from machine info)
- **Email**: (auto-filled from machine info)
- **Phone**: (auto-filled from machine info)
- **Software**: (auto-filled from machine info)
- **Version**: (auto-filled from machine info)

填写其余字段：
- **公司名称**：（从机器信息自动填充）
- **邮箱**：（从机器信息自动填充）
- **电话**：（从机器信息自动填充）
- **软件**：（从机器信息自动填充）
- **版本**：（从机器信息自动填充）

### Step 5: Set License Parameters / 步骤5：设置许可证参数

1. **License Type**: Choose from lease/demo/perpetual
2. **Start Date**: Set when license becomes active
3. **Expiration Date**: Set license validity period

1. **许可证类型**：从租赁/演示/永久中选择
2. **开始日期**：设置许可证生效时间
3. **到期日期**：设置许可证有效期

### Step 6: Generate License / 步骤6：生成许可证

1. **Click "Generate License"**
2. **Processing steps**:
   - Company ID validation/assignment
   - Company registry update
   - License generation with encryption
   - Digital signature creation

1. **点击"生成许可证"**
2. **处理步骤**：
   - 公司ID验证/分配
   - 公司注册表更新
   - 加密许可证生成
   - 数字签名创建

### Step 7: Review Generated License / 步骤7：查看生成的许可证

The generated license will include:

生成的许可证将包含：

```json
{
  "company_name": "Your Company Name",
  "email": "<EMAIL>",
  "phone": "18888888888",
  "company_id": "123-4567",                    // ← New: Readable Company ID
  "encrypted_company_id": "base64_data...",    // ← New: Encrypted Company ID
  "authorized_software": "Software Name",
  "authorized_version": "1.0.0",
  "license_type": "lease",
  "start_date": "2025-07-13",
  "expiration_date": "2026-01-13",
  "issued_date": "2025-07-13",
  "encrypted_machine_id": "base64_data...",
  "signature": "base64_signature..."           // ← Updated: Includes Company ID hash
}
```

### Step 8: Check Company Registry / 步骤8：检查公司注册表

After generation, check `company_registry.json`:

生成后，检查`company_registry.json`：

```json
{
  "version": "1.0",
  "last_updated": "2025-07-13T10:30:00Z",
  "next_available_id": 1000001,
  "companies": [
    {
      "company_id": "123-4567",
      "company_name": "Your Company Name",
      "email": "<EMAIL>",
      "phone": "18888888888",
      "created_date": "2025-07-13",
      "last_used": "2025-07-13",
      "notes": ""
    }
  ]
}
```

## Demo Results / 演示结果

### What's New / 新增内容

1. **Company ID Field**: User-friendly input with auto-formatting
2. **Registry Management**: Automatic company record management
3. **Enhanced Security**: Separate encryption for company IDs
4. **Improved Tracking**: Better license organization by company

1. **公司ID字段**：用户友好的输入和自动格式化
2. **注册表管理**：自动公司记录管理
3. **增强安全性**：公司ID独立加密
4. **改进跟踪**：按公司更好地组织许可证

### Benefits / 优势

- **Scalability**: Supports up to 900,000 unique company IDs
- **User-Friendly**: Intuitive input with real-time validation
- **Security**: Enhanced encryption and signature verification
- **Management**: Centralized company registry for easy tracking

- **可扩展性**：支持多达900,000个唯一公司ID
- **用户友好**：直观输入和实时验证
- **安全性**：增强的加密和签名验证
- **管理**：集中的公司注册表便于跟踪

## Testing Scenarios / 测试场景

### Scenario 1: New Company / 场景1：新公司

- Leave Company ID empty
- System assigns `100-0000`
- Company record created automatically

- 保持公司ID为空
- 系统分配`100-0000`
- 自动创建公司记录

### Scenario 2: Existing Company / 场景2：现有公司

- Same company name as before
- System finds existing record
- Uses existing Company ID

- 与之前相同的公司名称
- 系统找到现有记录
- 使用现有公司ID

### Scenario 3: Custom Company ID / 场景3：自定义公司ID

- Enter specific ID like `500-1234`
- System validates and assigns
- Creates new company record

- 输入特定ID如`500-1234`
- 系统验证并分配
- 创建新公司记录

---

**Demo Version**: V27  
**Demo Date**: 2025-07-13  
**Status**: Ready for Production Use
