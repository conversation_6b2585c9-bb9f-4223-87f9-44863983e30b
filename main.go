package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// Config represents the application configuration
type Config struct {
	Version         string `json:"version"`
	MachineInfoPath string `json:"machine_info_path"`
	PrivateKeyPath  string `json:"private_key_path"`
}

const configFileName = "config_license_generator_for_factory.json"

// openFileLocation opens the file location in the system file manager
func openFileLocation(filePath string) error {
	dir := filepath.Dir(filePath)

	switch runtime.GOOS {
	case "windows":
		// Use explorer to open the folder and select the file
		return exec.Command("explorer", "/select,", filePath).Start()
	case "darwin":
		// Use Finder on macOS
		return exec.Command("open", "-R", filePath).Start()
	case "linux":
		// Use the default file manager on Linux
		return exec.Command("xdg-open", dir).Start()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// ScreenResolution represents screen resolution information
type ScreenResolution struct {
	Width  int
	Height int
	DPI    float32
}

// Common screen resolutions and their optimal window sizes
var commonResolutions = map[string]ScreenResolution{
	"1024x768":  {1024, 768, 96},   // Old 4:3 monitors
	"1280x720":  {1280, 720, 96},   // HD 16:9
	"1280x800":  {1280, 800, 96},   // 16:10 laptops
	"1366x768":  {1366, 768, 96},   // Common laptop
	"1440x900":  {1440, 900, 96},   // 16:10 widescreen
	"1600x900":  {1600, 900, 96},   // HD+ 16:9
	"1680x1050": {1680, 1050, 96},  // WSXGA+ 16:10
	"1920x1080": {1920, 1080, 96},  // Full HD 16:9
	"1920x1200": {1920, 1200, 96},  // WUXGA 16:10
	"2560x1440": {2560, 1440, 144}, // QHD 16:9
	"2560x1600": {2560, 1600, 144}, // WQXGA 16:10
	"3840x2160": {3840, 2160, 192}, // 4K UHD 16:9
}

// calculateOptimalWindowSize determines the best window size based on screen resolution and OS
func calculateOptimalWindowSize() fyne.Size {
	// Get screen resolution (simplified approach)
	screenWidth, screenHeight := getScreenResolution()

	// Calculate optimal window size based on screen size
	var windowWidth, windowHeight float32

	// Base sizing strategy: use percentage of screen size
	if screenWidth <= 1024 {
		// Small screens (1024x768 and below)
		windowWidth = float32(screenWidth) * 0.85   // 85% of screen width
		windowHeight = float32(screenHeight) * 0.80 // 80% of screen height
	} else if screenWidth <= 1366 {
		// Medium screens (1366x768, common laptops)
		windowWidth = float32(screenWidth) * 0.75   // 75% of screen width
		windowHeight = float32(screenHeight) * 0.75 // 75% of screen height
	} else if screenWidth <= 1920 {
		// Large screens (1920x1080, Full HD)
		windowWidth = float32(screenWidth) * 0.60   // 60% of screen width
		windowHeight = float32(screenHeight) * 0.70 // 70% of screen height
	} else {
		// Very large screens (2K, 4K and above)
		windowWidth = float32(screenWidth) * 0.45   // 45% of screen width
		windowHeight = float32(screenHeight) * 0.60 // 60% of screen height
	}

	// Apply OS-specific adjustments
	switch runtime.GOOS {
	case "linux":
		// Linux often has different window decorations
		windowHeight += 40 // Account for title bar and borders
	case "darwin":
		// macOS has different window chrome
		windowHeight += 30 // Account for title bar
	case "windows":
		// Windows standard adjustments
		windowHeight += 35 // Account for title bar and borders
	}

	// Ensure minimum and maximum sizes
	minWidth, minHeight := float32(600), float32(500)
	maxWidth, maxHeight := float32(1200), float32(900)

	if windowWidth < minWidth {
		windowWidth = minWidth
	}
	if windowHeight < minHeight {
		windowHeight = minHeight
	}
	if windowWidth > maxWidth {
		windowWidth = maxWidth
	}
	if windowHeight > maxHeight {
		windowHeight = maxHeight
	}

	log.Printf("Screen resolution: %dx%d, Calculated window size: %.0fx%.0f",
		screenWidth, screenHeight, windowWidth, windowHeight)

	return fyne.NewSize(windowWidth, windowHeight)
}

// getScreenResolution attempts to get the screen resolution
func getScreenResolution() (int, int) {
	// Default fallback resolution
	defaultWidth, defaultHeight := 1920, 1080

	switch runtime.GOOS {
	case "windows":
		return getWindowsScreenResolution(defaultWidth, defaultHeight)
	case "linux":
		return getLinuxScreenResolution(defaultWidth, defaultHeight)
	case "darwin":
		return getMacOSScreenResolution(defaultWidth, defaultHeight)
	default:
		log.Printf("Unknown OS, using default resolution: %dx%d", defaultWidth, defaultHeight)
		return defaultWidth, defaultHeight
	}
}

// getWindowsScreenResolution gets screen resolution on Windows
func getWindowsScreenResolution(defaultWidth, defaultHeight int) (int, int) {
	// Try to get resolution using PowerShell
	cmd := exec.Command("powershell", "-Command",
		"Add-Type -AssemblyName System.Windows.Forms; "+
			"[System.Windows.Forms.Screen]::PrimaryScreen.Bounds.Width, "+
			"[System.Windows.Forms.Screen]::PrimaryScreen.Bounds.Height")

	output, err := cmd.Output()
	if err != nil {
		log.Printf("Failed to get Windows screen resolution: %v", err)
		return defaultWidth, defaultHeight
	}

	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	if len(lines) >= 2 {
		width := strings.TrimSpace(lines[0])
		height := strings.TrimSpace(lines[1])

		if w, err := fmt.Sscanf(width, "%d", &defaultWidth); w == 1 && err == nil {
			if h, err := fmt.Sscanf(height, "%d", &defaultHeight); h == 1 && err == nil {
				return defaultWidth, defaultHeight
			}
		}
	}

	log.Printf("Could not parse Windows screen resolution, using default: %dx%d", defaultWidth, defaultHeight)
	return defaultWidth, defaultHeight
}

// getLinuxScreenResolution gets screen resolution on Linux
func getLinuxScreenResolution(defaultWidth, defaultHeight int) (int, int) {
	// Try xrandr first
	cmd := exec.Command("xrandr", "--current")
	output, err := cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "*") && strings.Contains(line, "x") {
				// Parse line like "1920x1080     60.00*+  59.93"
				parts := strings.Fields(line)
				if len(parts) > 0 {
					resolution := parts[0]
					if resParts := strings.Split(resolution, "x"); len(resParts) == 2 {
						if w, err := fmt.Sscanf(resParts[0], "%d", &defaultWidth); w == 1 && err == nil {
							if h, err := fmt.Sscanf(resParts[1], "%d", &defaultHeight); h == 1 && err == nil {
								return defaultWidth, defaultHeight
							}
						}
					}
				}
			}
		}
	}

	// Fallback: try xdpyinfo
	cmd = exec.Command("xdpyinfo")
	output, err = cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "dimensions:") {
				// Parse line like "  dimensions:    1920x1080 pixels (508x285 millimeters)"
				parts := strings.Fields(line)
				for _, part := range parts {
					if strings.Contains(part, "x") && !strings.Contains(part, "millimeters") {
						if resParts := strings.Split(part, "x"); len(resParts) == 2 {
							if w, err := fmt.Sscanf(resParts[0], "%d", &defaultWidth); w == 1 && err == nil {
								if h, err := fmt.Sscanf(resParts[1], "%d", &defaultHeight); h == 1 && err == nil {
									return defaultWidth, defaultHeight
								}
							}
						}
					}
				}
			}
		}
	}

	log.Printf("Could not get Linux screen resolution, using default: %dx%d", defaultWidth, defaultHeight)
	return defaultWidth, defaultHeight
}

// getMacOSScreenResolution gets screen resolution on macOS
func getMacOSScreenResolution(defaultWidth, defaultHeight int) (int, int) {
	// Try to get resolution using system_profiler
	cmd := exec.Command("system_profiler", "SPDisplaysDataType")
	output, err := cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "Resolution:") {
				// Parse line like "          Resolution: 1920 x 1080"
				parts := strings.Fields(line)
				if len(parts) >= 4 {
					if w, err := fmt.Sscanf(parts[1], "%d", &defaultWidth); w == 1 && err == nil {
						if h, err := fmt.Sscanf(parts[3], "%d", &defaultHeight); h == 1 && err == nil {
							return defaultWidth, defaultHeight
						}
					}
				}
			}
		}
	}

	log.Printf("Could not get macOS screen resolution, using default: %dx%d", defaultWidth, defaultHeight)
	return defaultWidth, defaultHeight
}

// loadConfig loads configuration from file
func loadConfig() *Config {
	config := &Config{}

	data, err := os.ReadFile(configFileName)
	if err != nil {
		// If config file doesn't exist, return empty config
		return config
	}

	err = json.Unmarshal(data, config)
	if err != nil {
		log.Printf("Error parsing config file: %v", err)
		return &Config{}
	}

	return config
}

// saveConfig saves configuration to file
func saveConfig(config *Config) error {
	// Ensure version is set if not already present
	if config.Version == "" {
		config.Version = "v23.0"
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}

	err = os.WriteFile(configFileName, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write config file: %v", err)
	}

	return nil
}

// showWindowsFileDialog shows a native Windows file dialog
func showWindowsFileDialog(title, filter string) (string, error) {
	// Use PowerShell to show native Windows file dialog
	var cmd *exec.Cmd
	if filter == "json" {
		cmd = exec.Command("powershell", "-Command",
			`Add-Type -AssemblyName System.Windows.Forms; `+
				`$dialog = New-Object System.Windows.Forms.OpenFileDialog; `+
				`$dialog.Title = "`+title+`"; `+
				`$dialog.Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*"; `+
				`$dialog.FilterIndex = 1; `+
				`if ($dialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) { `+
				`Write-Output $dialog.FileName } else { Write-Output "" }`)
	} else if filter == "pem" {
		cmd = exec.Command("powershell", "-Command",
			`Add-Type -AssemblyName System.Windows.Forms; `+
				`$dialog = New-Object System.Windows.Forms.OpenFileDialog; `+
				`$dialog.Title = "`+title+`"; `+
				`$dialog.Filter = "PEM files (*.pem)|*.pem|All files (*.*)|*.*"; `+
				`$dialog.FilterIndex = 1; `+
				`if ($dialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) { `+
				`Write-Output $dialog.FileName } else { Write-Output "" }`)
	} else {
		return "", fmt.Errorf("unsupported filter: %s", filter)
	}

	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	result := strings.TrimSpace(string(output))
	return result, nil
}

// showWindowsSaveDialog shows a native Windows save dialog
func showWindowsSaveDialog(title, defaultName string) (string, error) {
	cmd := exec.Command("powershell", "-Command",
		`Add-Type -AssemblyName System.Windows.Forms; `+
			`$dialog = New-Object System.Windows.Forms.SaveFileDialog; `+
			`$dialog.Title = "`+title+`"; `+
			`$dialog.Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*"; `+
			`$dialog.FilterIndex = 1; `+
			`$dialog.FileName = "`+defaultName+`"; `+
			`$dialog.OverwritePrompt = $true; `+
			`$dialog.CheckPathExists = $true; `+
			`if ($dialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) { `+
			`Write-Output $dialog.FileName } else { Write-Output "" }`)

	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	result := strings.TrimSpace(string(output))
	return result, nil
}

// extractSoftwareAndVersion extracts software name and version from GeneratedBy field
// Example: "LS-DYNA Model License Generate Factory v2.3.0" -> ("LS-DYNA Model License Generate Factory", "2.3.0")
func extractSoftwareAndVersion(generatedBy string) (string, string) {
	// Look for version pattern like "v2.3.0" at the end
	parts := strings.Fields(generatedBy)
	if len(parts) == 0 {
		return generatedBy, ""
	}

	// Check if the last part starts with 'v' and looks like a version
	lastPart := parts[len(parts)-1]
	if strings.HasPrefix(lastPart, "v") && len(lastPart) > 1 {
		// Extract version (remove 'v' prefix)
		version := lastPart[1:]
		// Extract software name (everything except the last part)
		softwareName := strings.Join(parts[:len(parts)-1], " ")
		return softwareName, version
	}

	// If no version pattern found, return the whole string as software name
	return generatedBy, ""
}

// updateMachineID updates the machine ID display when a valid path is provided
func updateMachineID(machineInfoPath, privateKeyPath string, machineIDEntry *widget.Entry) {
	if machineInfoPath == "" || privateKeyPath == "" {
		machineIDEntry.SetText("")
		return
	}

	// Try to decrypt machine ID for display
	machineInfo, err := LoadMachineInfo(machineInfoPath)
	if err != nil {
		machineIDEntry.SetText("Error loading machine info")
		return
	}

	privateKey, err := LoadPrivateKey(privateKeyPath)
	if err != nil {
		machineIDEntry.SetText("Error loading private key")
		return
	}

	decryptedMachineID, err := DecryptMachineID(machineInfo.MachineID, privateKey)
	if err != nil {
		machineIDEntry.SetText("Error decrypting machine ID")
		return
	}

	// Display both decrypted and encrypted (for user verification)
	displayText := fmt.Sprintf("Decrypted: %s\nEncrypted: %s...",
		decryptedMachineID,
		machineInfo.MachineID[:min(len(machineInfo.MachineID), 40)])
	machineIDEntry.SetText(displayText)
}

func main() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Application panic: %v", r)
			log.Println("Press Enter to exit...")
			var input string
			_, _ = fmt.Scanln(&input)
		}
	}()

	log.Println("Starting License Generator...")

	myApp := app.New()

	// Load configuration first to get version
	config := loadConfig()

	// Create window title with version
	windowTitle := "License Generator"
	if config.Version != "" {
		windowTitle = fmt.Sprintf("License Generator %s", config.Version)
	}

	window := myApp.NewWindow(windowTitle)

	// Adaptive window sizing based on screen resolution and OS
	windowSize := calculateOptimalWindowSize()
	window.Resize(windowSize)
	window.CenterOnScreen()

	// Set minimum window size to ensure usability
	window.SetFixedSize(false)
	minSize := fyne.NewSize(600, 500) // Minimum usable size
	window.Canvas().SetOnTypedKey(func(event *fyne.KeyEvent) {
		// Allow window resizing but maintain minimum size
		if window.Canvas().Size().Width < minSize.Width || window.Canvas().Size().Height < minSize.Height {
			if window.Canvas().Size().Width < minSize.Width {
				window.Resize(fyne.NewSize(minSize.Width, window.Canvas().Size().Height))
			}
			if window.Canvas().Size().Height < minSize.Height {
				window.Resize(fyne.NewSize(window.Canvas().Size().Width, minSize.Height))
			}
		}
	})

	// File selection
	machineInfoEntry := widget.NewEntry()
	machineInfoEntry.SetPlaceHolder("Select machine info JSON file...")
	if config.MachineInfoPath != "" {
		machineInfoEntry.SetText(config.MachineInfoPath)
	}

	privateKeyEntry := widget.NewEntry()
	privateKeyEntry.SetPlaceHolder("Select private key PEM file...")
	if config.PrivateKeyPath != "" {
		privateKeyEntry.SetText(config.PrivateKeyPath)
	}

	// Machine ID display
	machineIDEntry := widget.NewEntry()
	machineIDEntry.SetPlaceHolder("Decrypted Machine ID will appear here...")
	machineIDEntry.Disable()

	// License details
	companyEntry := widget.NewEntry()
	companyEntry.SetPlaceHolder("Company Name")

	emailEntry := widget.NewEntry()
	emailEntry.SetPlaceHolder("Email Address")

	phoneEntry := widget.NewEntry()
	phoneEntry.SetPlaceHolder("Phone Number")

	softwareEntry := widget.NewEntry()
	softwareEntry.SetPlaceHolder("Authorized Software")

	versionEntry := widget.NewEntry()
	versionEntry.SetPlaceHolder("Version")

	// License Type selection with radio buttons
	licenseTypeOptions := []string{"lease", "demo", "perpetual"}
	licenseTypeRadio := widget.NewRadioGroup(licenseTypeOptions, nil)
	licenseTypeRadio.SetSelected("lease") // Default to lease
	licenseTypeRadio.Horizontal = true    // Display horizontally

	// Start Date selection - User-friendly design with default to today
	startDateOptions := []string{
		"📅 Today (Current Date)",
		"📅 Tomorrow",
		"📅 Next Week",
		"📅 Next Month",
		"🗓️ Custom Start Date",
	}
	startDateSelect := widget.NewSelect(startDateOptions, nil)
	startDateSelect.SetSelected("📅 Today (Current Date)") // Default to today

	// Start Date custom date selection (similar to expiration date)
	startYearSelect := widget.NewSelect([]string{}, nil)
	startMonthSelect := widget.NewSelect([]string{}, nil)
	startDaySelect := widget.NewSelect([]string{}, nil)

	// Populate start date year options (current year to next 5 years)
	currentYear := time.Now().Year()
	for year := currentYear; year <= currentYear+5; year++ {
		startYearSelect.Options = append(startYearSelect.Options, fmt.Sprintf("%d", year))
	}
	startYearSelect.SetSelected(fmt.Sprintf("%d", currentYear)) // Default to current year

	// Populate start date month options
	startMonthSelect.Options = []string{
		"01", "02", "03", "04", "05", "06",
		"07", "08", "09", "10", "11", "12",
	}
	startMonthSelect.SetSelected(fmt.Sprintf("%02d", time.Now().Month())) // Default to current month

	// Function to update start date day options based on selected year and month
	var updateStartDayOptions func()
	updateStartDayOptions = func() {
		if startYearSelect.Selected == "" || startMonthSelect.Selected == "" {
			return
		}

		year, _ := strconv.Atoi(startYearSelect.Selected)
		month, _ := strconv.Atoi(startMonthSelect.Selected)

		// Calculate days in the selected month
		daysInMonth := time.Date(year, time.Month(month+1), 0, 0, 0, 0, 0, time.UTC).Day()

		startDaySelect.Options = []string{}
		for day := 1; day <= daysInMonth; day++ {
			startDaySelect.Options = append(startDaySelect.Options, fmt.Sprintf("%02d", day))
		}
		startDaySelect.SetSelected(fmt.Sprintf("%02d", time.Now().Day())) // Default to current day
		startDaySelect.Refresh()
	}

	// Set up change handlers for start date dropdowns
	startYearSelect.OnChanged = func(string) { updateStartDayOptions() }
	startMonthSelect.OnChanged = func(string) { updateStartDayOptions() }

	// Initialize start date day options
	updateStartDayOptions()

	// Custom start date panel container
	customStartDatePanel := container.NewGridWithColumns(3,
		container.NewVBox(widget.NewLabel("Year"), startYearSelect),
		container.NewVBox(widget.NewLabel("Month"), startMonthSelect),
		container.NewVBox(widget.NewLabel("Day"), startDaySelect),
	)
	customStartDatePanel.Hide() // Initially hidden

	// Start date preview label
	startDatePreviewLabel := widget.NewLabel("")
	startDatePreviewLabel.Alignment = fyne.TextAlignCenter

	// Function to update start date preview
	var updateStartDatePreview func()
	updateStartDatePreview = func() {
		selectedPreset := startDateSelect.Selected
		var previewDate time.Time

		if selectedPreset == "🗓️ Custom Start Date" {
			if startYearSelect.Selected != "" && startMonthSelect.Selected != "" && startDaySelect.Selected != "" {
				yearStr := startYearSelect.Selected
				monthStr := startMonthSelect.Selected
				dayStr := startDaySelect.Selected

				customDate := fmt.Sprintf("%s-%s-%s", yearStr, monthStr, dayStr)
				if parsedDate, err := time.Parse("2006-01-02", customDate); err == nil {
					previewDate = parsedDate
				}
			}
		} else {
			// Calculate start date based on preset
			switch selectedPreset {
			case "📅 Today (Current Date)":
				previewDate = time.Now()
			case "📅 Tomorrow":
				previewDate = time.Now().AddDate(0, 0, 1)
			case "📅 Next Week":
				previewDate = time.Now().AddDate(0, 0, 7)
			case "📅 Next Month":
				previewDate = time.Now().AddDate(0, 1, 0)
			default:
				previewDate = time.Now()
			}
		}

		if !previewDate.IsZero() {
			startDatePreviewLabel.SetText(fmt.Sprintf("Start Date: %s", previewDate.Format("2006-01-02")))
		} else {
			startDatePreviewLabel.SetText("Start Date: Please select a valid date")
		}
	}

	// Set up change handlers for start date selection
	startDateSelect.OnChanged = func(selected string) {
		if selected == "🗓️ Custom Start Date" {
			customStartDatePanel.Show()
		} else {
			customStartDatePanel.Hide()
		}
		updateStartDatePreview()
	}

	startYearSelect.OnChanged = func(string) {
		updateStartDayOptions()
		updateStartDatePreview()
	}
	startMonthSelect.OnChanged = func(string) {
		updateStartDayOptions()
		updateStartDatePreview()
	}
	startDaySelect.OnChanged = func(string) { updateStartDatePreview() }

	// Initialize start date preview
	updateStartDatePreview()

	// Expiration date - Enhanced user-friendly design
	expirationOptions := []string{
		"📅 1 Month (30 days)",
		"📅 3 Months (90 days)",
		"📅 6 Months (180 days)",
		"📅 1 Year (365 days)",
		"📅 2 Years (730 days)",
		"🗓️ Custom Date",
	}
	expirationSelect := widget.NewSelect(expirationOptions, nil)
	expirationSelect.SetSelected(expirationOptions[2]) // Default to 6 months for compatibility

	// Custom date selection with dropdown boxes
	currentYearForExpiration := time.Now().Year()

	// Year dropdown (current year to current year + 10)
	var yearOptions []string
	for i := currentYearForExpiration; i <= currentYearForExpiration+10; i++ {
		yearOptions = append(yearOptions, fmt.Sprintf("%d", i))
	}
	yearSelect := widget.NewSelect(yearOptions, nil)
	yearSelect.SetSelected(fmt.Sprintf("%d", currentYearForExpiration+1)) // Default to next year
	yearSelect.Disable()

	// Month dropdown
	monthOptions := []string{
		"01 - January", "02 - February", "03 - March", "04 - April",
		"05 - May", "06 - June", "07 - July", "08 - August",
		"09 - September", "10 - October", "11 - November", "12 - December",
	}
	monthSelect := widget.NewSelect(monthOptions, nil)
	monthSelect.SetSelected("12 - December") // Default to December
	monthSelect.Disable()

	// Day dropdown (1-31, will be updated based on month/year)
	daySelect := widget.NewSelect([]string{}, nil)
	daySelect.Disable()

	// Function to update day options based on selected month and year
	updateDayOptions := func() {
		if yearSelect.Selected == "" || monthSelect.Selected == "" {
			return
		}

		var year, month int
		fmt.Sscanf(yearSelect.Selected, "%d", &year)
		monthStr := monthSelect.Selected[:2] // Extract month number
		fmt.Sscanf(monthStr, "%d", &month)

		// Calculate days in the selected month
		daysInMonth := time.Date(year, time.Month(month+1), 0, 0, 0, 0, 0, time.UTC).Day()

		var dayOptions []string
		for i := 1; i <= daysInMonth; i++ {
			dayOptions = append(dayOptions, fmt.Sprintf("%02d", i))
		}

		daySelect.Options = dayOptions
		if len(dayOptions) > 0 {
			daySelect.SetSelected(dayOptions[len(dayOptions)-1]) // Default to last day of month
		}
		daySelect.Refresh()
	}

	// Initialize day options
	updateDayOptions()

	// Custom date panel container
	customDatePanel := container.NewGridWithColumns(3,
		container.NewVBox(widget.NewLabel("Year"), yearSelect),
		container.NewVBox(widget.NewLabel("Month"), monthSelect),
		container.NewVBox(widget.NewLabel("Day"), daySelect),
	)
	customDatePanel.Hide() // Initially hidden

	// Preview label to show calculated expiration date
	expirationPreviewLabel := widget.NewLabel("")
	expirationPreviewLabel.Alignment = fyne.TextAlignCenter

	// Function to update preview
	var updateExpirationPreview func()
	updateExpirationPreview = func() {
		selected := expirationSelect.Selected
		if selected == "" {
			return
		}

		var previewText string
		if selected == "🗓️ Custom Date" {
			// Get custom date from dropdowns
			if yearSelect.Selected != "" && monthSelect.Selected != "" && daySelect.Selected != "" {
				yearStr := yearSelect.Selected
				monthStr := monthSelect.Selected[:2] // Extract month number
				dayStr := daySelect.Selected

				dateStr := fmt.Sprintf("%s-%s-%s", yearStr, monthStr, dayStr)
				if parsedDate, err := time.Parse("2006-01-02", dateStr); err == nil {
					previewText = fmt.Sprintf("📅 Expires: %s", parsedDate.Format("January 2, 2006"))
				} else {
					previewText = "❌ Invalid date combination"
				}
			} else {
				previewText = "📝 Select year, month, and day above"
			}
		} else {
			// Calculate preview for preset options
			var days int
			switch selected {
			case "📅 1 Month (30 days)":
				days = 30
			case "📅 3 Months (90 days)":
				days = 90
			case "📅 6 Months (180 days)":
				days = 180
			case "📅 1 Year (365 days)":
				days = 365
			case "📅 2 Years (730 days)":
				days = 730
			}
			futureDate := time.Now().AddDate(0, 0, days)
			previewText = fmt.Sprintf("📅 Expires: %s", futureDate.Format("January 2, 2006"))
		}
		expirationPreviewLabel.SetText(previewText)
	}

	// Update day options when year or month changes
	yearSelect.OnChanged = func(string) { updateDayOptions(); updateExpirationPreview() }
	monthSelect.OnChanged = func(string) { updateDayOptions(); updateExpirationPreview() }
	daySelect.OnChanged = func(string) { updateExpirationPreview() }

	// Initialize preview
	updateExpirationPreview()

	expirationSelect.OnChanged = func(selected string) {
		if selected == "🗓️ Custom Date" {
			// Enable custom date dropdowns
			yearSelect.Enable()
			monthSelect.Enable()
			daySelect.Enable()
			customDatePanel.Show()
		} else {
			// Disable custom date dropdowns
			yearSelect.Disable()
			monthSelect.Disable()
			daySelect.Disable()
			customDatePanel.Hide()
		}
		updateExpirationPreview()
	}

	// Initialize preview
	updateExpirationPreview()

	// Output
	outputText := widget.NewMultiLineEntry()
	outputText.SetPlaceHolder("Generated license will appear here...")
	outputText.Disable()

	var currentLicense *LicenseData

	// Generate button
	// Create a status label for visual feedback
	statusLabel := widget.NewLabel("")
	statusLabel.Hide()

	var generateBtn *widget.Button
	generateBtn = widget.NewButton("Generate License", func() {
		log.Println("Generate License clicked")

		// Show immediate visual feedback
		statusLabel.SetText("🔄 Generating license, please wait...")
		statusLabel.Show()
		generateBtn.SetText("⏳ Generating...")
		generateBtn.Disable()

		// Refresh the UI to show changes immediately
		window.Content().Refresh()

		defer func() {
			generateBtn.SetText("Generate License")
			generateBtn.Enable()
			statusLabel.Hide()
			window.Content().Refresh()
		}()

		// Validate inputs
		if machineInfoEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("Please select a machine info file"), window)
			return
		}

		if privateKeyEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("Please select a private key file"), window)
			return
		}

		if companyEntry.Text == "" || emailEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("Please fill in company name and email"), window)
			return
		}

		if softwareEntry.Text == "" || versionEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("Please fill in software name and version"), window)
			return
		}

		// Calculate start date with enhanced logic
		var startDate time.Time
		selectedStartPreset := startDateSelect.Selected

		if selectedStartPreset == "🗓️ Custom Start Date" {
			// Get date from dropdown selections
			if startYearSelect.Selected == "" || startMonthSelect.Selected == "" || startDaySelect.Selected == "" {
				dialog.ShowError(fmt.Errorf("Please select year, month, and day for custom start date"), window)
				return
			}

			startYearStr := startYearSelect.Selected
			startMonthStr := startMonthSelect.Selected
			startDayStr := startDaySelect.Selected

			customStartDate := fmt.Sprintf("%s-%s-%s", startYearStr, startMonthStr, startDayStr)
			parsedStartDate, err := time.Parse("2006-01-02", customStartDate)
			if err != nil {
				dialog.ShowError(fmt.Errorf("Invalid start date combination selected"), window)
				return
			}

			startDate = parsedStartDate
		} else {
			// Calculate start date based on preset options
			switch selectedStartPreset {
			case "📅 Today (Current Date)":
				startDate = time.Now()
			case "📅 Tomorrow":
				startDate = time.Now().AddDate(0, 0, 1)
			case "📅 Next Week":
				startDate = time.Now().AddDate(0, 0, 7)
			case "📅 Next Month":
				startDate = time.Now().AddDate(0, 1, 0)
			default:
				// Fallback to today
				startDate = time.Now()
			}
		}

		// Calculate expiration date with enhanced logic
		var expirationDate time.Time
		selectedPreset := expirationSelect.Selected

		if selectedPreset == "🗓️ Custom Date" {
			// Get date from dropdown selections
			if yearSelect.Selected == "" || monthSelect.Selected == "" || daySelect.Selected == "" {
				dialog.ShowError(fmt.Errorf("Please select year, month, and day for custom date"), window)
				return
			}

			yearStr := yearSelect.Selected
			monthStr := monthSelect.Selected[:2] // Extract month number
			dayStr := daySelect.Selected

			customDate := fmt.Sprintf("%s-%s-%s", yearStr, monthStr, dayStr)
			parsedDate, err := time.Parse("2006-01-02", customDate)
			if err != nil {
				dialog.ShowError(fmt.Errorf("Invalid date combination selected"), window)
				return
			}

			// Check if date is in the future
			if parsedDate.Before(time.Now()) {
				dialog.ShowError(fmt.Errorf("Expiration date must be in the future"), window)
				return
			}

			expirationDate = parsedDate
		} else {
			// Calculate expiration based on new preset options
			var days int
			switch selectedPreset {
			case "📅 1 Month (30 days)":
				days = 30
			case "📅 3 Months (90 days)":
				days = 90
			case "📅 6 Months (180 days)":
				days = 180
			case "📅 1 Year (365 days)":
				days = 365
			case "📅 2 Years (730 days)":
				days = 730
			default:
				// Fallback to 6 months for compatibility
				days = 180
			}
			expirationDate = time.Now().AddDate(0, 0, days)
		}

		// Validate dates
		if expirationDate.Before(time.Now()) {
			dialog.ShowError(fmt.Errorf("Expiration date must be in the future"), window)
			return
		}

		// Validate that expiration date is after start date
		if expirationDate.Before(startDate) || expirationDate.Equal(startDate) {
			dialog.ShowError(fmt.Errorf("⚠️ Date Validation Error\n\nExpiration date (%s) must be later than start date (%s).\n\nPlease adjust your dates to ensure the license has a valid duration.",
				expirationDate.Format("2006-01-02"),
				startDate.Format("2006-01-02")), window)
			return
		}

		// Show friendly confirmation for date range
		daysDuration := int(expirationDate.Sub(startDate).Hours() / 24)
		log.Printf("License duration: %d days (from %s to %s)",
			daysDuration,
			startDate.Format("2006-01-02"),
			expirationDate.Format("2006-01-02"))

		// Update status: Loading machine info
		statusLabel.SetText("📂 Loading machine information...")
		window.Content().Refresh()

		// Force reload machine info file to get latest data
		log.Printf("Reloading machine info from: %s", machineInfoEntry.Text)
		freshMachineInfo, err := LoadMachineInfo(machineInfoEntry.Text)
		if err != nil {
			statusLabel.SetText("❌ Failed to load machine info")
			window.Content().Refresh()
			dialog.ShowError(fmt.Errorf("Failed to reload machine info: %v", err), window)
			return
		}

		// Update status: Processing data
		statusLabel.SetText("⚙️ Processing machine data...")
		window.Content().Refresh()

		// Update GUI fields with fresh data from file
		companyEntry.SetText(freshMachineInfo.CompanyName)
		emailEntry.SetText(freshMachineInfo.Email)
		phoneEntry.SetText(freshMachineInfo.Phone)

		// Extract software name and version from fresh GeneratedBy field
		freshSoftwareName, freshVersion := extractSoftwareAndVersion(freshMachineInfo.GeneratedBy)
		softwareEntry.SetText(freshSoftwareName)
		versionEntry.SetText(freshVersion)

		log.Printf("Updated with fresh data - Company: %s, Email: %s", freshMachineInfo.CompanyName, freshMachineInfo.Email)

		// Update status: Initializing generator
		statusLabel.SetText("🔧 Initializing license generator...")
		window.Content().Refresh()

		// Generate license with separate keys
		// Use existing private key for machine decryption (unchanged)
		// Use new signing key for digital signatures
		generator, err := NewLicenseGenerator(
			machineInfoEntry.Text,             // machine info file
			privateKeyEntry.Text,              // machine decryption key (unchanged)
			"license_signing_private_key.pem", // new signing key
		)
		if err != nil {
			statusLabel.SetText("❌ Failed to initialize generator")
			window.Content().Refresh()
			dialog.ShowError(fmt.Errorf("Failed to initialize license generator: %v", err), window)
			return
		}

		// Update status: Generating license
		statusLabel.SetText("🔐 Generating license with encryption...")
		window.Content().Refresh()

		// Generate license with fresh data from file
		license, err := generator.GenerateLicense(
			freshMachineInfo.CompanyName, // Use fresh data from file
			freshMachineInfo.Email,       // Use fresh data from file
			freshMachineInfo.Phone,       // Use fresh data from file
			freshSoftwareName,            // Use fresh extracted software name
			freshVersion,                 // Use fresh extracted version
			licenseTypeRadio.Selected,    // Use selected license type
			startDate,                    // Use calculated start date
			expirationDate,
		)
		if err != nil {
			statusLabel.SetText("❌ Failed to generate license")
			window.Content().Refresh()
			dialog.ShowError(fmt.Errorf("Failed to generate license: %v", err), window)
			return
		}

		currentLicense = license
		outputText.SetText(FormatLicenseForDisplay(license))

		// Update status: Opening save dialog
		statusLabel.SetText("💾 Opening save dialog...")
		window.Content().Refresh()

		// Immediately show native Windows save dialog
		savePath, err := showWindowsSaveDialog("Save License File", "factory_license.json")
		if err != nil {
			statusLabel.SetText("❌ Failed to open save dialog")
			window.Content().Refresh()
			dialog.ShowError(fmt.Errorf("Failed to open save dialog: %v", err), window)
			return
		}
		if savePath != "" {
			// Update status: Saving file
			statusLabel.SetText("💾 Saving license file...")
			window.Content().Refresh()

			log.Printf("Saving license to: %s", savePath)

			// Check if file exists and log it
			if _, err := os.Stat(savePath); err == nil {
				log.Printf("File exists, will overwrite: %s", savePath)
			}

			err = SaveLicenseToFile(currentLicense, savePath)
			if err != nil {
				statusLabel.SetText("❌ Failed to save license file")
				window.Content().Refresh()
				log.Printf("Failed to save license: %v", err)
				dialog.ShowError(fmt.Errorf("Failed to save license: %v", err), window)
				return
			}

			log.Printf("License saved successfully to: %s", savePath)

			// Update status: Success
			statusLabel.SetText("✅ License saved successfully!")
			window.Content().Refresh()

			// Open file location in system file manager
			go func() {
				if err := openFileLocation(savePath); err != nil {
					log.Printf("Failed to open file location: %v", err)
				} else {
					log.Printf("Opened file location: %s", filepath.Dir(savePath))
				}
			}()

			// Create custom dialog with English button and file location info
			successMessage := fmt.Sprintf("License generated and saved successfully!\n\n📁 File: %s\n🗂️ Folder opened automatically\n\nCompany: %s\nSoftware: %s v%s\n🏷️ Type: %s\n📅 Start: %s\n⏰ Expires: %s",
				filepath.Base(savePath),
				currentLicense.CompanyName,
				currentLicense.AuthorizedSoftware,
				currentLicense.AuthorizedVersion,
				licenseTypeRadio.Selected,
				startDate.Format("2006-01-02"),
				currentLicense.ExpirationDate)
			successDialog := dialog.NewCustom("Success", "OK", widget.NewLabel(successMessage), window)
			successDialog.Show()
		} else {
			statusLabel.SetText("⚠️ Save operation cancelled")
			window.Content().Refresh()
			log.Println("User cancelled save dialog")
		}
	})
	generateBtn.Importance = widget.HighImportance

	// File selection buttons with native Windows dialogs
	selectMachineBtn := widget.NewButton("Browse", func() {
		path, err := showWindowsFileDialog("Select Machine Info JSON File", "json")
		if err != nil {
			dialog.ShowError(fmt.Errorf("Failed to open file dialog: %v", err), window)
			return
		}
		if path != "" {
			machineInfoEntry.SetText(path)

			// Save to config
			config.MachineInfoPath = path
			err := saveConfig(config)
			if err != nil {
				log.Printf("Failed to save config: %v", err)
			}

			// Auto-populate fields
			machineInfo, err := LoadMachineInfo(path)
			if err == nil {
				companyEntry.SetText(machineInfo.CompanyName)
				emailEntry.SetText(machineInfo.Email)
				phoneEntry.SetText(machineInfo.Phone)

				// Extract software name and version from GeneratedBy field
				// Example: "LS-DYNA Model License Generate Factory v2.3.0"
				generatedBy := machineInfo.GeneratedBy
				softwareName, version := extractSoftwareAndVersion(generatedBy)

				softwareEntry.SetText(softwareName)
				versionEntry.SetText(version)
			}

			// Update machine ID display
			updateMachineID(machineInfoEntry.Text, privateKeyEntry.Text, machineIDEntry)
		}
	})

	selectKeyBtn := widget.NewButton("Browse", func() {
		path, err := showWindowsFileDialog("Select Private Key PEM File", "pem")
		if err != nil {
			dialog.ShowError(fmt.Errorf("Failed to open file dialog: %v", err), window)
			return
		}
		if path != "" {
			privateKeyEntry.SetText(path)

			// Save to config
			config.PrivateKeyPath = path
			err := saveConfig(config)
			if err != nil {
				log.Printf("Failed to save config: %v", err)
			}

			// Update machine ID display
			updateMachineID(machineInfoEntry.Text, privateKeyEntry.Text, machineIDEntry)
		}
	})

	// Layout
	content := container.NewVBox(
		widget.NewCard("File Selection", "",
			container.NewVBox(
				widget.NewLabel("Machine Info File:"),
				container.NewBorder(nil, nil, nil, selectMachineBtn, machineInfoEntry),
				widget.NewLabel("Private Key File:"),
				container.NewBorder(nil, nil, nil, selectKeyBtn, privateKeyEntry),
				widget.NewLabel("Decrypted Machine ID:"),
				machineIDEntry,
			),
		),

		widget.NewCard("License Details", "",
			container.NewVBox(
				container.NewGridWithColumns(2, companyEntry, emailEntry),
				container.NewGridWithColumns(2, phoneEntry, softwareEntry),
				versionEntry,
			),
		),

		widget.NewCard("🏷️ License Type", "",
			container.NewVBox(
				widget.NewLabel("Select license type:"),
				licenseTypeRadio,
			),
		),

		widget.NewCard("📅 Start Date", "",
			container.NewVBox(
				widget.NewLabel("Select when the license becomes active:"),
				startDateSelect,
				customStartDatePanel,
				widget.NewSeparator(),
				startDatePreviewLabel,
			),
		),

		widget.NewCard("⏰ Expiration Date", "",
			container.NewVBox(
				widget.NewLabel("Select license validity period:"),
				expirationSelect,
				customDatePanel,
				widget.NewSeparator(),
				expirationPreviewLabel,
			),
		),

		widget.NewCard("Generated License", "",
			container.NewVBox(
				outputText,
				statusLabel,
				generateBtn,
			),
		),
	)

	scrollContainer := container.NewScroll(content)
	window.SetContent(scrollContainer)

	// Auto-load default files if they exist in config
	if config.MachineInfoPath != "" && config.PrivateKeyPath != "" {
		// Try to load machine info and auto-populate fields
		machineInfo, err := LoadMachineInfo(config.MachineInfoPath)
		if err == nil {
			companyEntry.SetText(machineInfo.CompanyName)
			emailEntry.SetText(machineInfo.Email)
			phoneEntry.SetText(machineInfo.Phone)

			// Extract software name and version from GeneratedBy field
			generatedBy := machineInfo.GeneratedBy
			softwareName, version := extractSoftwareAndVersion(generatedBy)

			softwareEntry.SetText(softwareName)
			versionEntry.SetText(version)

			// Update machine ID display
			updateMachineID(config.MachineInfoPath, config.PrivateKeyPath, machineIDEntry)
		}
	}

	log.Println("Showing window...")
	window.ShowAndRun()
}
