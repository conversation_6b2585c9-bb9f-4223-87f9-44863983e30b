// 测试V9版本生成的license是否正确
package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// 数据结构定义
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// 辅助函数
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func loadPrivateKey() (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile("machine_decryption_private_key_to_decryp_factory_machineinfo.pem")
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %v", err)
	}
	
	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}
	
	return privateKey, nil
}

func validateLicense(licenseFile string) error {
	fmt.Printf("🔍 Validating License: %s\n", licenseFile)
	fmt.Println("=" + fmt.Sprintf("%*s", len(licenseFile)+20, "="))
	
	// 1. 加载许可证文件
	licenseData, err := os.ReadFile(licenseFile)
	if err != nil {
		return fmt.Errorf("failed to read license file: %v", err)
	}
	
	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		return fmt.Errorf("failed to parse license: %v", err)
	}
	
	fmt.Printf("📋 License Info:\n")
	fmt.Printf("  Company: %s\n", license.CompanyName)
	fmt.Printf("  Email: %s\n", license.Email)
	fmt.Printf("  Software: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  Expiration: %s\n", license.ExpirationDate)
	fmt.Printf("  Issued: %s\n", license.IssuedDate)
	
	// 2. 加载私钥
	privateKey, err := loadPrivateKey()
	if err != nil {
		return fmt.Errorf("failed to load private key: %v", err)
	}
	
	// 3. 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}
	
	decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}
	
	fmt.Printf("🔓 Decrypted Machine ID: %s\n", string(decryptedMachineID))
	
	// 4. 重构签名数据
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}
	
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(string(decryptedMachineID)),
	}
	
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}
	
	fmt.Printf("📋 Signature JSON: %s\n", string(jsonData))
	
	// 5. 验证签名
	hash := sha256.Sum256(jsonData)
	
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}
	
	publicKey := &privateKey.PublicKey
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	
	fmt.Println("✅ Signature verification successful!")
	return nil
}

func compareLicenses() {
	fmt.Println("🔍 Comparing License Files")
	fmt.Println("==========================")
	
	// 测试成功的fresh_factory_license.json
	if _, err := os.Stat("fresh_factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing fresh_factory_license.json (Known Good)...")
		err := validateLicense("fresh_factory_license.json")
		if err != nil {
			fmt.Printf("❌ Fresh license validation failed: %v\n", err)
		} else {
			fmt.Println("✅ Fresh license validation successful!")
		}
	}
	
	// 测试GUI生成的factory_license.json
	if _, err := os.Stat("factory_license.json"); err == nil {
		fmt.Println("\n🧪 Testing factory_license.json (GUI Generated)...")
		err := validateLicense("factory_license.json")
		if err != nil {
			fmt.Printf("❌ GUI license validation failed: %v\n", err)
		} else {
			fmt.Println("✅ GUI license validation successful!")
		}
	}
	
	fmt.Println("\n📋 Comparison Summary:")
	fmt.Println("- fresh_factory_license.json: Generated with correct logic")
	fmt.Println("- factory_license.json: Generated with GUI (should now be fixed)")
	fmt.Println("- Both should pass validation if V9 fix is correct")
}

func main() {
	fmt.Println("🚀 Testing V9 License Generation Fix")
	fmt.Println("====================================")
	
	fmt.Println("Please use the V9 GUI to generate a new factory_license.json")
	fmt.Println("Then this script will compare it with the known good fresh_factory_license.json")
	fmt.Println("")
	
	compareLicenses()
}
