# Factory License Generation - Source Code Files
# 工厂许可证生成 - 源代码文件

## 📁 Core Source Files / 核心源文件

### 1. models.go - Data Structures / 数据结构
```go
// Contains all data structures used in license generation
// 包含许可证生成中使用的所有数据结构

- LicenseData struct
- MachineInfo struct  
- SignatureData struct
- ExpirationPreset struct
```

### 2. crypto.go - Cryptographic Functions / 加密函数
```go
// Contains all cryptographic operations
// 包含所有加密操作

- LoadPrivateKey()
- LoadMachineInfo()
- EncryptMachineID()
- CreateSignature()
- SaveLicenseToFile()
- hashString()
```

### 3. license.go - License Generation Logic / 许可证生成逻辑
```go
// Contains license generation business logic
// 包含许可证生成业务逻辑

- LicenseGenerator struct
- NewLicenseGenerator()
- GenerateLicense()
```

### 4. main.go - GUI Application / GUI应用程序
```go
// Contains GUI interface and main application logic
// 包含GUI界面和主应用程序逻辑

- extractSoftwareAndVersion()
- GetExpirationPresets()
- CalculateExpirationDate()
- FormatLicenseForDisplay()
- showWindowsFileDialog()
- showWindowsSaveDialog()
- updateMachineID()
- Main GUI implementation
```

## 🔑 Key Files / 密钥文件

### RSA Private Key / RSA私钥
```
File: machine_decryption_private_key_to_decryp_factory_machineinfo.pem
Purpose: Used for signing and machine ID encryption
Format: PKCS#1 PEM
Size: 2048 bits
```

### RSA Public Key / RSA公钥
```
File: public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem
Purpose: Used for signature verification (reference)
Format: PKCS#1 PEM
Size: 2048 bits
```

## 📄 Data Files / 数据文件

### Machine Information Input / 机器信息输入
```json
File: factory_machine_info.json
Format: JSON
Contains:
{
  "CompanyName": "Li auto2",
  "Email": "<EMAIL>", 
  "Phone": "18101928290",
  "GeneratedBy": "LS-DYNA Model License Generate Factory v2.3.0",
  "MachineID": "711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104"
}
```

### Generated License Output / 生成的许可证输出
```json
File: factory_license.json
Format: JSON
Contains:
{
  "company_name": "Li auto2",
  "email": "<EMAIL>",
  "phone": "18101928290", 
  "authorized_software": "LS-DYNA Model License Generate Factory",
  "authorized_version": "2.3.0",
  "expiration_date": "2025-08-10",
  "issued_date": "2025-07-09",
  "encrypted_machine_id": "base64-encoded-encrypted-data",
  "signature": "base64-encoded-digital-signature"
}
```

## 🔧 Build Configuration / 构建配置

### Go Module / Go模块
```go
File: go.mod
module license-generator

go 1.21.1

require fyne.io/fyne/v2 v2.4.0
```

### Dependencies / 依赖项
```go
File: go.sum
// Contains all dependency checksums
// 包含所有依赖项校验和
```

## 📚 Documentation Files / 文档文件

### Integration Reference / 集成参考
```
- standalone_license_validator.go - Complete validation implementation
- LICENSE_VALIDATOR_INTEGRATION_GUIDE.md - Integration guide
- MACHINE_ID_IMPLEMENTATION_GUIDE.md - Machine ID implementation
- README_FOR_AUTHORIZED_SOFTWARE.md - Quick start guide
```

### Technical Documentation / 技术文档
```
- SIGNATURE_DATA_CONSTRUCTION_GUIDE.md - Signature construction details
- SIGNATURE_CONSTRUCTION_SUMMARY.md - Signature summary
- LICENSE_SIGNATURE_KEYS_INFO.md - Key information
- KEY_VERIFICATION_SUMMARY.md - Key verification results
```

### Version History / 版本历史
```
- V5_FRESH_RELOAD_FEATURE.md - V5 features
- V6_UI_IMPROVEMENTS.md - V6 improvements  
- V7_ENHANCED_VISUAL_FEEDBACK.md - V7 enhancements
```

## 🎯 For Authorized Software Developers / 被授权软件开发者

### Essential Files to Analyze / 需要分析的核心文件

#### 1. Core Logic Files / 核心逻辑文件
```
Priority 1 (Must Have):
- models.go - Understand data structures
- crypto.go - Understand encryption/signing
- license.go - Understand generation logic

Priority 2 (Important):
- standalone_license_validator.go - Complete validation reference
- FACTORY_LICENSE_GENERATION_CODE.md - Complete code reference
```

#### 2. Integration Files / 集成文件
```
For Integration:
- LICENSE_VALIDATOR_INTEGRATION_GUIDE.md
- MACHINE_ID_IMPLEMENTATION_GUIDE.md
- README_FOR_AUTHORIZED_SOFTWARE.md
- example_integration.go
```

#### 3. Key Files / 密钥文件
```
Security Critical:
- license_signature_private_key.pem - For embedding in validator
- license_signature_public_key.pem - For reference
- LICENSE_SIGNATURE_KEYS_INFO.md - Key usage information
```

### Analysis Checklist / 分析检查清单

#### Data Structure Analysis / 数据结构分析
- [ ] Understand LicenseData structure
- [ ] Understand SignatureData structure  
- [ ] Understand field mappings and JSON tags
- [ ] Understand date formats and constraints

#### Cryptographic Analysis / 加密分析
- [ ] Understand RSA key usage (signing + encryption)
- [ ] Understand signature construction process
- [ ] Understand machine ID encryption method
- [ ] Understand hash functions used

#### Integration Analysis / 集成分析
- [ ] Understand machine ID requirements
- [ ] Understand validation process flow
- [ ] Understand error handling patterns
- [ ] Understand file format requirements

#### Security Analysis / 安全分析
- [ ] Understand key protection requirements
- [ ] Understand signature verification process
- [ ] Understand machine binding mechanism
- [ ] Understand expiration enforcement

## 🚀 Quick Start for Analysis / 快速开始分析

### Step 1: Read Core Documentation / 第1步：阅读核心文档
```
1. FACTORY_LICENSE_GENERATION_CODE.md - Complete overview
2. README_FOR_AUTHORIZED_SOFTWARE.md - Quick start
3. LICENSE_VALIDATOR_INTEGRATION_GUIDE.md - Detailed integration
```

### Step 2: Analyze Core Code / 第2步：分析核心代码
```
1. models.go - Data structures
2. crypto.go - Cryptographic functions
3. license.go - Generation logic
4. standalone_license_validator.go - Validation reference
```

### Step 3: Understand Security Model / 第3步：理解安全模型
```
1. RSA-2048 key pair for dual purpose (signing + encryption)
2. Machine ID binding for hardware locking
3. Digital signatures for tamper protection
4. Expiration dates for time limiting
```

### Step 4: Plan Integration / 第4步：规划集成
```
1. Implement getCurrentMachineID() function
2. Embed RSA keys in validation code
3. Implement license validation logic
4. Add error handling and user feedback
```

---

**All source code and documentation is provided for complete analysis and integration.**

**提供所有源代码和文档以进行完整分析和集成。**
